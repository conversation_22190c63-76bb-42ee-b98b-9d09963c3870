# IAFFBO 客户端参数传输状态监控

## 概述

为了帮助您监控每个客户端的参数上传和下载状态，我们提供了两个监控工具：

1. **实时监控脚本** (`monitor_client_status.py`) - 实时显示客户端状态
2. **状态检查脚本** (`check_client_status.py`) - 快速分析日志文件

## 工具说明

### 1. 实时监控脚本 (`monitor_client_status.py`)

**功能：**
- 实时监控每个客户端的参数上传/下载状态
- 显示成功/失败统计
- 显示最近活动记录
- 自动刷新显示

**使用方法：**
```bash
# 使用默认设置
python monitor_client_status.py

# 指定日志文件和刷新间隔
python monitor_client_status.py --log-file out/logs/pyfmto.log --interval 3
```

**显示内容：**
- 客户端ID
- 上传成功/失败次数
- 下载成功/失败次数
- 最后上传/下载时间
- 当前状态
- 最近活动记录

### 2. 状态检查脚本 (`check_client_status.py`)

**功能：**
- 快速分析日志文件
- 统计所有客户端的传输状态
- 诊断常见问题
- 生成详细报告

**使用方法：**
```bash
# 使用默认日志文件
python check_client_status.py

# 指定日志文件
python check_client_status.py out/logs/pyfmto.log
```

**输出内容：**
- 每个客户端的详细统计
- 总体成功率
- 问题诊断
- 服务器处理情况

## 监控指标说明

### 客户端指标

1. **上传尝试** - 客户端尝试上传参数的次数
2. **上传成功** - 成功上传参数的次数
3. **上传失败** - 上传参数失败的次数
4. **下载尝试** - 客户端尝试下载参数的次数
5. **下载成功** - 成功下载参数的次数
6. **下载失败** - 下载参数失败的次数

### 服务器指标

1. **服务器收到** - 服务器接收到参数的次数
2. **服务器发送** - 服务器发送聚合参数的次数

## 日志格式

系统会在日志中记录以下关键事件：

### 客户端日志
```
Client X starting PUSH_WEIGHTS    # 开始上传参数
Client X PUSH_WEIGHTS SUCCESS     # 上传成功
Client X PUSH_WEIGHTS FAILED      # 上传失败
Client X starting PULL_WEIGHTS    # 开始下载参数
Client X PULL_WEIGHTS SUCCESS     # 下载成功
Client X PULL_WEIGHTS FAILED      # 下载失败
```

### 服务器日志
```
Server received weights from Client X     # 服务器接收参数
Server sent aggregated weights to Client X # 服务器发送聚合参数
```

## 常见问题诊断

### 1. 参数上传问题

**症状：** 上传成功率为0%
**可能原因：**
- 客户端权重为None（分类器训练失败）
- 网络连接问题
- 服务器响应格式错误

**解决方法：**
- 检查分类器训练日志
- 检查网络连接
- 查看详细错误信息

### 2. 参数下载问题

**症状：** 下载成功率为0%
**可能原因：**
- 服务器没有可用的聚合参数
- 版本不匹配
- 聚合过程失败

**解决方法：**
- 确保有客户端成功上传参数
- 检查服务器聚合日志
- 验证版本同步

### 3. 服务器处理异常

**症状：** 服务器接收次数 ≠ 客户端上传成功次数
**可能原因：**
- 客户端响应检查逻辑错误
- 服务器处理异常
- 日志记录不一致

## 性能优化建议

1. **减少重试次数** - 如果网络稳定，可以减少重试次数
2. **调整重试间隔** - 根据网络延迟调整重试间隔
3. **批量处理** - 考虑批量上传/下载以提高效率

## 示例输出

### 实时监控示例
```
============================================================
IAFFBO 客户端参数传输状态监控 - 2024-01-15 10:30:45
============================================================
客户端    上传成功  上传失败  下载成功  下载失败  最后上传    最后下载    上传状态   下载状态
------------------------------------------------------------
Client 1  5/8      3        4/7      3        10:29:30   10:30:15   Success   Success
Client 2  6/9      3        5/8      3        10:29:45   10:30:30   Success   Success
...
```

### 状态检查示例
```
====================================================================================================
IAFFBO 客户端参数传输状态统计
====================================================================================================
客户端      上传尝试     上传成功     上传失败     下载尝试     下载成功     下载失败     服务器收到      服务器发送
----------------------------------------------------------------------------------------------------
Client 1  8        5        3        7        4        3        5          4
Client 2  9        6        3        8        5        3        6          5
...

分析结果:
- 活跃客户端数量: 18
- 上传成功率: 62.5% (100/160)
- 下载成功率: 58.3% (84/144)
- 服务器接收参数次数: 100
- 服务器发送参数次数: 84
```

## 注意事项

1. **日志文件路径** - 确保日志文件路径正确
2. **权限问题** - 确保有读取日志文件的权限
3. **实时性** - 实时监控会持续运行，按Ctrl+C退出
4. **资源占用** - 监控脚本占用资源很少，可以长期运行

## 技术支持

如果遇到问题，请检查：
1. 日志文件是否存在且可读
2. Python环境是否正确
3. 相关依赖是否安装

更多技术细节请参考源代码注释。
