#!/usr/bin/env python3
"""
修复 IAFFBO 结果文件中的客户端ID格式问题
将字符串格式的客户端ID转换为整数格式，以匹配 pyfmto reporter 的期望格式
"""
import msgpack
import os
import shutil
from pathlib import Path

def fix_msgpack_file(filepath):
    """修复单个 msgpack 文件的客户端ID格式"""
    print(f"修复文件: {filepath}")
    
    try:
        # 读取原始数据
        with open(str(filepath), 'rb') as f:
            data = msgpack.unpack(f, raw=False, strict_map_key=False)
        
        if isinstance(data, dict) and '_solutions' in data:
            solutions = data['_solutions']
            if isinstance(solutions, dict):
                # 检查是否需要修复（如果键是字符串格式）
                keys = list(solutions.keys())
                if keys and isinstance(keys[0], str):
                    print(f"  发现字符串格式的客户端ID: {keys}")
                    
                    # 创建新的solutions字典，将字符串键转换为整数键
                    new_solutions = {}
                    for str_key, value in solutions.items():
                        try:
                            int_key = int(str_key)
                            new_solutions[int_key] = value
                        except ValueError:
                            print(f"  警告: 无法转换键 '{str_key}' 为整数，保持原样")
                            new_solutions[str_key] = value
                    
                    # 更新数据
                    data['_solutions'] = new_solutions
                    
                    # 备份原文件
                    backup_path = str(filepath) + '.backup'
                    shutil.copy2(str(filepath), backup_path)
                    print(f"  已备份原文件到: {backup_path}")
                    
                    # 保存修复后的数据
                    with open(str(filepath), 'wb') as f:
                        msgpack.pack(data, f)
                    
                    print(f"  修复完成: {len(new_solutions)} 个客户端ID已转换")
                    return True
                else:
                    print(f"  文件已是正确格式，无需修复")
                    return False
            else:
                print(f"  警告: _solutions 不是字典格式")
                return False
        else:
            print(f"  警告: 文件格式不符合预期")
            return False
            
    except Exception as e:
        print(f"  错误: 修复文件失败 - {e}")
        return False

def fix_directory(directory_path):
    """修复目录中所有的 msgpack 文件"""
    directory = Path(directory_path)
    if not directory.exists():
        print(f"目录不存在: {directory_path}")
        return
    
    print(f"\n修复目录: {directory_path}")
    
    fixed_count = 0
    total_count = 0
    
    # 遍历所有 .msgpack 文件
    for msgpack_file in directory.rglob("*.msgpack"):
        total_count += 1
        if fix_msgpack_file(msgpack_file):
            fixed_count += 1
    
    print(f"\n目录修复完成: {fixed_count}/{total_count} 个文件已修复")

def main():
    # 修复 IAFFBO ARXIV2017 目录
    iaffbo_arxiv_path = "out/results/IAFFBO/ARXIV2017"
    if os.path.exists(iaffbo_arxiv_path):
        fix_directory(iaffbo_arxiv_path)
    else:
        print(f"IAFFBO ARXIV2017 目录不存在: {iaffbo_arxiv_path}")
    
    # 修复 IAFFBO ARXIV2017_10D 目录（如果存在）
    iaffbo_arxiv_10d_path = "out/results/IAFFBO/ARXIV2017_10D"
    if os.path.exists(iaffbo_arxiv_10d_path):
        fix_directory(iaffbo_arxiv_10d_path)
    else:
        print(f"IAFFBO ARXIV2017_10D 目录不存在: {iaffbo_arxiv_10d_path}")

if __name__ == "__main__":
    main()
