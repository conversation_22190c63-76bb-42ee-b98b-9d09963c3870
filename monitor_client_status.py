#!/usr/bin/env python3
"""
IAFFBO客户端参数上传/下载状态监控脚本

实时监控每个客户端的：
1. 参数上传状态 (PUSH_WEIGHTS)
2. 参数下载状态 (PULL_WEIGHTS)
3. 成功/失败统计
4. 时间统计
"""

import re
import time
import os
from collections import defaultdict, deque
from datetime import datetime
import argparse


class ClientStatusMonitor:
    def __init__(self, log_file="out/logs/pyfmto.log"):
        self.log_file = log_file
        self.client_stats = defaultdict(lambda: {
            'push_success': 0,
            'push_failed': 0,
            'pull_success': 0,
            'pull_failed': 0,
            'last_push_time': None,
            'last_pull_time': None,
            'last_push_status': 'Unknown',
            'last_pull_status': 'Unknown',
            'push_attempts': deque(maxlen=10),  # 最近10次尝试
            'pull_attempts': deque(maxlen=10)
        })
        
        # 正则表达式模式
        self.patterns = {
            'push_start': re.compile(r'Client (\d+) starting PUSH_WEIGHTS'),
            'push_success': re.compile(r'Client (\d+) PUSH_WEIGHTS SUCCESS'),
            'push_failed': re.compile(r'Client (\d+) PUSH_WEIGHTS FAILED'),
            'pull_start': re.compile(r'Client (\d+) starting PULL_WEIGHTS'),
            'pull_success': re.compile(r'Client (\d+) PULL_WEIGHTS SUCCESS'),
            'pull_failed': re.compile(r'Client (\d+) PULL_WEIGHTS FAILED'),
            'server_received': re.compile(r'Server received weights from Client (\d+)'),
            'server_sent': re.compile(r'Server sent aggregated weights to Client (\d+)'),
            'timestamp': re.compile(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})')
        }
        
        self.last_position = 0
        
    def parse_log_line(self, line):
        """解析日志行"""
        timestamp_match = self.patterns['timestamp'].search(line)
        timestamp = timestamp_match.group(1) if timestamp_match else None
        
        events = []
        
        # 检查各种事件
        for event_type, pattern in self.patterns.items():
            if event_type == 'timestamp':
                continue
                
            match = pattern.search(line)
            if match:
                client_id = int(match.group(1))
                events.append({
                    'type': event_type,
                    'client_id': client_id,
                    'timestamp': timestamp,
                    'line': line.strip()
                })
        
        return events
    
    def update_stats(self, event):
        """更新客户端统计信息"""
        client_id = event['client_id']
        event_type = event['type']
        timestamp = event['timestamp']
        
        stats = self.client_stats[client_id]
        
        if event_type == 'push_start':
            stats['push_attempts'].append({'time': timestamp, 'status': 'started'})
        elif event_type == 'push_success':
            stats['push_success'] += 1
            stats['last_push_time'] = timestamp
            stats['last_push_status'] = 'Success'
            if stats['push_attempts']:
                stats['push_attempts'][-1]['status'] = 'success'
        elif event_type == 'push_failed':
            stats['push_failed'] += 1
            stats['last_push_time'] = timestamp
            stats['last_push_status'] = 'Failed'
            if stats['push_attempts']:
                stats['push_attempts'][-1]['status'] = 'failed'
        elif event_type == 'pull_start':
            stats['pull_attempts'].append({'time': timestamp, 'status': 'started'})
        elif event_type == 'pull_success':
            stats['pull_success'] += 1
            stats['last_pull_time'] = timestamp
            stats['last_pull_status'] = 'Success'
            if stats['pull_attempts']:
                stats['pull_attempts'][-1]['status'] = 'success'
        elif event_type == 'pull_failed':
            stats['pull_failed'] += 1
            stats['last_pull_time'] = timestamp
            stats['last_pull_status'] = 'Failed'
            if stats['pull_attempts']:
                stats['pull_attempts'][-1]['status'] = 'failed'
    
    def read_new_logs(self):
        """读取新的日志内容"""
        if not os.path.exists(self.log_file):
            return []
        
        events = []
        try:
            with open(self.log_file, 'r', encoding='utf-8') as f:
                f.seek(self.last_position)
                new_lines = f.readlines()
                self.last_position = f.tell()
                
                for line in new_lines:
                    line_events = self.parse_log_line(line)
                    events.extend(line_events)
        except Exception as e:
            print(f"读取日志文件出错: {e}")
        
        return events
    
    def display_status(self):
        """显示客户端状态"""
        os.system('clear' if os.name == 'posix' else 'cls')
        
        print("=" * 120)
        print(f"IAFFBO 客户端参数传输状态监控 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 120)
        
        if not self.client_stats:
            print("暂无客户端数据...")
            return
        
        # 表头
        print(f"{'客户端':<8} {'上传成功':<8} {'上传失败':<8} {'下载成功':<8} {'下载失败':<8} "
              f"{'最后上传':<12} {'最后下载':<12} {'上传状态':<10} {'下载状态':<10}")
        print("-" * 120)
        
        # 按客户端ID排序
        for client_id in sorted(self.client_stats.keys()):
            stats = self.client_stats[client_id]
            
            # 计算成功率
            push_total = stats['push_success'] + stats['push_failed']
            pull_total = stats['pull_success'] + stats['pull_failed']
            
            push_rate = f"{stats['push_success']}/{push_total}" if push_total > 0 else "0/0"
            pull_rate = f"{stats['pull_success']}/{pull_total}" if pull_total > 0 else "0/0"
            
            last_push = stats['last_push_time'][-8:] if stats['last_push_time'] else "未知"
            last_pull = stats['last_pull_time'][-8:] if stats['last_pull_time'] else "未知"
            
            print(f"Client {client_id:<2} {push_rate:<8} {stats['push_failed']:<8} "
                  f"{pull_rate:<8} {stats['pull_failed']:<8} "
                  f"{last_push:<12} {last_pull:<12} "
                  f"{stats['last_push_status']:<10} {stats['last_pull_status']:<10}")
        
        # 总体统计
        print("-" * 120)
        total_clients = len(self.client_stats)
        total_push_success = sum(stats['push_success'] for stats in self.client_stats.values())
        total_push_failed = sum(stats['push_failed'] for stats in self.client_stats.values())
        total_pull_success = sum(stats['pull_success'] for stats in self.client_stats.values())
        total_pull_failed = sum(stats['pull_failed'] for stats in self.client_stats.values())
        
        print(f"总计: {total_clients} 个客户端")
        print(f"上传: 成功 {total_push_success}, 失败 {total_push_failed}")
        print(f"下载: 成功 {total_pull_success}, 失败 {total_pull_failed}")
        
        # 显示最近活动
        print("\n最近活动:")
        recent_events = []
        for client_id, stats in self.client_stats.items():
            for attempt in list(stats['push_attempts'])[-3:]:
                recent_events.append(f"Client {client_id} PUSH {attempt['status']} at {attempt['time']}")
            for attempt in list(stats['pull_attempts'])[-3:]:
                recent_events.append(f"Client {client_id} PULL {attempt['status']} at {attempt['time']}")
        
        for event in sorted(recent_events)[-10:]:  # 显示最近10个事件
            print(f"  {event}")
    
    def run(self, refresh_interval=2):
        """运行监控"""
        print("开始监控IAFFBO客户端状态...")
        print(f"日志文件: {self.log_file}")
        print(f"刷新间隔: {refresh_interval}秒")
        print("按 Ctrl+C 退出")
        
        try:
            while True:
                # 读取新日志
                events = self.read_new_logs()
                
                # 更新统计
                for event in events:
                    self.update_stats(event)
                
                # 显示状态
                self.display_status()
                
                # 等待
                time.sleep(refresh_interval)
                
        except KeyboardInterrupt:
            print("\n监控已停止")


def main():
    parser = argparse.ArgumentParser(description='IAFFBO客户端状态监控')
    parser.add_argument('--log-file', default='out/logs/pyfmto.log', 
                       help='日志文件路径 (默认: out/logs/pyfmto.log)')
    parser.add_argument('--interval', type=int, default=2,
                       help='刷新间隔(秒) (默认: 2)')
    
    args = parser.parse_args()
    
    monitor = ClientStatusMonitor(args.log_file)
    monitor.run(args.interval)


if __name__ == "__main__":
    main()
