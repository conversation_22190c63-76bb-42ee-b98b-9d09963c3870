client:
  lg_type: L<PERSON>
  max_gen: 20
  epoch: 5               # local epoch
  optimizer: sgd         # optimizer of RBF network, sgd/m-sgd/max-gd
  lr: 0.06               # learning rate
  alpha: 1.0             # noisy
server:
  ensemble_size: 20
  epoch: 5               # local epoch
  optimizer: sgd         # optimizer of RBF network, sgd/m-sgd/max-gd
  lr: 0.06               # learning rate
  alpha: 1.0             # noisy
