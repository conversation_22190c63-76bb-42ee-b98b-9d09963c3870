
                               ____                __         
            ____     __  __   / __/  ____ ___     / /_   ____ 
           / __ \   / / / /  / /_   / __ `__ \   / __/  / __ \
          / /_/ /  / /_/ /  / __/  / / / / / /  / /_   / /_/ /
         / .___/   \__, /  /_/    /_/ /_/ /_/   \__/   \____/ 
        /_/       /____/                                      

INFO    2025-08-05 09:48:25           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 09:48:25           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 09:48:25           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 09:48:25           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 09:48:25           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 09:48:25           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 09:48:25           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 09:48:25           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 09:48:25           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 09:48:25           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 09:48:25           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 09:48:25           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 09:48:25           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 09:48:25           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 09:48:25           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 09:48:25           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 09:48:25           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 09:48:25           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-05 09:48:25        launcher.py->line(168)|
╭───────────┬──────────┬────────────────┬─────────────┬───────────┬───────┬───────────┬────────╮
│  running  │  repeat  │    progress    │  algorithm  │  problem  │  iid  │  clients  │  save  │
├───────────┼──────────┼────────────────┼─────────────┼───────────┼───────┼───────────┼────────┤
│    1/1    │   4/20   │ [4/20][20.00%] │   IAFFBO    │ arxiv2017 │   2   │    18     │  True  │
╰───────────┴──────────┴────────────────┴─────────────┴───────────┴───────┴───────────┴────────╯
INFO    2025-08-05 09:48:25        launcher.py->line(185)|Server started.
INFO    2025-08-05 09:48:27          client.py->line(111)|Client 1  started
INFO    2025-08-05 09:48:27          client.py->line(111)|Client 2  started
INFO    2025-08-05 09:48:27          client.py->line(111)|Client 3  started
INFO    2025-08-05 09:48:27          client.py->line(111)|Client 4  started
INFO    2025-08-05 09:48:27          client.py->line(111)|Client 5  started
INFO    2025-08-05 09:48:27          client.py->line(111)|Client 6  started
INFO    2025-08-05 09:48:27          client.py->line(111)|Client 7  started
INFO    2025-08-05 09:48:27          client.py->line(111)|Client 8  started
INFO    2025-08-05 09:48:27          client.py->line(111)|Client 9  started
INFO    2025-08-05 09:48:27          client.py->line(111)|Client 10 started
INFO    2025-08-05 09:48:27          client.py->line(111)|Client 11 started
INFO    2025-08-05 09:48:27          client.py->line(111)|Client 12 started
INFO    2025-08-05 09:48:27          client.py->line(111)|Client 13 started
INFO    2025-08-05 09:48:27          client.py->line(111)|Client 14 started
INFO    2025-08-05 09:48:27          client.py->line(111)|Client 15 started
INFO    2025-08-05 09:48:27          client.py->line(111)|Client 16 started
INFO    2025-08-05 09:48:27          client.py->line(111)|Client 17 started
INFO    2025-08-05 09:48:27          client.py->line(111)|Client 18 started
INFO    2025-08-05 09:48:27           tools.py->line(207)|
===================== IAFFBOServer ====================
╭───────────────────┬───────────┬───────────┬─────────╮
│ Parameter         │  Default  │  Updates  │  Using  │
├───────────────────┼───────────┼───────────┼─────────┤
│ n_clusters        │     6     │     6     │    6    │
├───────────────────┼───────────┼───────────┼─────────┤
│ device            │     -     │   cuda    │    -    │
├───────────────────┼───────────┼───────────┼─────────┤
│ n_samples         │    100    │    100    │   100   │
├───────────────────┼───────────┼───────────┼─────────┤
│ batch_aggregation │     -     │   True    │    -    │
├───────────────────┼───────────┼───────────┼─────────┤
│ agg_interval      │    0.3    │    0.3    │   0.3   │
╰───────────────────┴───────────┴───────────┴─────────╯
ERROR   2025-08-05 09:48:28          client.py->line(205)|Client 1  Connection refused 1 times.
ERROR   2025-08-05 09:48:28          client.py->line(205)|Client 3  Connection refused 1 times.
ERROR   2025-08-05 09:48:28          client.py->line(205)|Client 2  Connection refused 1 times.
INFO    2025-08-05 09:48:28          server.py->line(162)|Client 1 join, total 1 clients
ERROR   2025-08-05 09:48:28          client.py->line(205)|Client 5  Connection refused 1 times.
INFO    2025-08-05 09:48:28          server.py->line(162)|Client 2 join, total 2 clients
INFO    2025-08-05 09:48:28          client.py->line(89)|
==================== Client 1  Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Griewank   │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-05 09:48:28          server.py->line(162)|Client 5 join, total 3 clients
INFO    2025-08-05 09:48:28          client.py->line(89)|
==================== Client 2  Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Rastrigin  │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-05 09:48:28          server.py->line(162)|Client 3 join, total 4 clients
INFO    2025-08-05 09:48:28          client.py->line(89)|
==================== Client 5  Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Ackley     │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-05 09:48:28          client.py->line(89)|
==================== Client 3  Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Ackley     │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
ERROR   2025-08-05 09:48:28          client.py->line(205)|Client 6  Connection refused 1 times.
ERROR   2025-08-05 09:48:28          client.py->line(205)|Client 4  Connection refused 1 times.
ERROR   2025-08-05 09:48:28          client.py->line(205)|Client 9  Connection refused 1 times.
INFO    2025-08-05 09:48:28          server.py->line(162)|Client 6 join, total 5 clients
ERROR   2025-08-05 09:48:28          client.py->line(205)|Client 10 Connection refused 1 times.
INFO    2025-08-05 09:48:28          server.py->line(162)|Client 4 join, total 6 clients
ERROR   2025-08-05 09:48:28          client.py->line(205)|Client 13 Connection refused 1 times.
INFO    2025-08-05 09:48:28          client.py->line(89)|
==================== Client 6  Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Schwefel   │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
ERROR   2025-08-05 09:48:28          client.py->line(205)|Client 7  Connection refused 1 times.
ERROR   2025-08-05 09:48:28          client.py->line(205)|Client 14 Connection refused 1 times.
INFO    2025-08-05 09:48:28          server.py->line(162)|Client 13 join, total 7 clients
INFO    2025-08-05 09:48:28          server.py->line(162)|Client 10 join, total 8 clients
INFO    2025-08-05 09:48:28          client.py->line(89)|
==================== Client 4  Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Rastrigin  │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-05 09:48:28          server.py->line(162)|Client 9 join, total 9 clients
INFO    2025-08-05 09:48:28          server.py->line(162)|Client 7 join, total 10 clients
ERROR   2025-08-05 09:48:28          client.py->line(205)|Client 8  Connection refused 1 times.
ERROR   2025-08-05 09:48:28          client.py->line(205)|Client 11 Connection refused 1 times.
ERROR   2025-08-05 09:48:28          client.py->line(205)|Client 12 Connection refused 1 times.
INFO    2025-08-05 09:48:28          server.py->line(162)|Client 14 join, total 11 clients
ERROR   2025-08-05 09:48:28          client.py->line(205)|Client 17 Connection refused 1 times.
INFO    2025-08-05 09:48:28          server.py->line(162)|Client 8 join, total 12 clients
ERROR   2025-08-05 09:48:28          client.py->line(205)|Client 16 Connection refused 1 times.
INFO    2025-08-05 09:48:28          server.py->line(162)|Client 12 join, total 13 clients
INFO    2025-08-05 09:48:28          server.py->line(162)|Client 11 join, total 14 clients
ERROR   2025-08-05 09:48:28          client.py->line(205)|Client 15 Connection refused 1 times.
ERROR   2025-08-05 09:48:28          client.py->line(205)|Client 18 Connection refused 1 times.
INFO    2025-08-05 09:48:28          server.py->line(162)|Client 16 join, total 15 clients
INFO    2025-08-05 09:48:28          client.py->line(89)|
==================== Client 13 Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Rosenbrock │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-05 09:48:28          server.py->line(162)|Client 17 join, total 16 clients
INFO    2025-08-05 09:48:28          client.py->line(89)|
==================== Client 10 Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Rosenbrock │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-05 09:48:28          server.py->line(162)|Client 15 join, total 17 clients
INFO    2025-08-05 09:48:28          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-05 09:48:28          client.py->line(89)|
==================== Client 9  Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Ackley     │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-05 09:48:28          client.py->line(89)|
==================== Client 7  Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Rastrigin  │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-05 09:48:28          client.py->line(89)|
==================== Client 14 Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Rastrigin  │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-05 09:48:28          client.py->line(89)|
==================== Client 8  Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Sphere     │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-05 09:48:28          client.py->line(89)|
==================== Client 11 Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Ackley     │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-05 09:48:28          client.py->line(89)|
===================== Client 12 Params ====================
╭─────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName    │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├─────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Weierstrass │    10 │     1 │     2 │      50 │     110 │
╰─────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-05 09:48:28          client.py->line(89)|
===================== Client 16 Params ====================
╭─────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName    │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├─────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Weierstrass │    10 │     1 │     2 │      50 │     110 │
╰─────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-05 09:48:28          client.py->line(89)|
==================== Client 17 Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Rastrigin  │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-05 09:48:28          client.py->line(89)|
==================== Client 15 Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Griewank   │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-05 09:48:28          client.py->line(89)|
==================== Client 18 Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Schwefel   │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-05 09:48:28   iaffbo_client.py->line(232)|Client 7 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-05 09:48:28   iaffbo_client.py->line(232)|Client 3 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-05 09:48:28   iaffbo_client.py->line(232)|Client 1 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-05 09:48:28   iaffbo_client.py->line(232)|Client 11 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:48:28   iaffbo_client.py->line(267)|Client 3 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.005s
WARNING 2025-08-05 09:48:28   iaffbo_client.py->line(267)|Client 7 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.009s
WARNING 2025-08-05 09:48:28   iaffbo_client.py->line(267)|Client 1 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.009s
INFO    2025-08-05 09:48:28   iaffbo_client.py->line(232)|Client 16 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-05 09:48:28   iaffbo_client.py->line(232)|Client 2 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:48:28   iaffbo_client.py->line(267)|Client 11 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.02s, avg_attempt=0.021s
WARNING 2025-08-05 09:48:28   iaffbo_client.py->line(267)|Client 16 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.009s
WARNING 2025-08-05 09:48:28   iaffbo_client.py->line(267)|Client 2 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.010s
INFO    2025-08-05 09:48:28   iaffbo_client.py->line(232)|Client 4 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-05 09:48:28   iaffbo_client.py->line(232)|Client 9 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-05 09:48:28   iaffbo_client.py->line(232)|Client 15 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:48:28   iaffbo_client.py->line(267)|Client 4 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.02s, avg_attempt=0.022s
INFO    2025-08-05 09:48:28   iaffbo_client.py->line(232)|Client 12 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:48:28   iaffbo_client.py->line(267)|Client 9 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.02s, avg_attempt=0.019s
INFO    2025-08-05 09:48:28   iaffbo_client.py->line(232)|Client 5 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-05 09:48:28   iaffbo_client.py->line(232)|Client 14 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:48:28   iaffbo_client.py->line(267)|Client 15 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.03s, avg_attempt=0.027s
WARNING 2025-08-05 09:48:28   iaffbo_client.py->line(267)|Client 12 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.02s, avg_attempt=0.022s
INFO    2025-08-05 09:48:28   iaffbo_client.py->line(232)|Client 10 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:48:28   iaffbo_client.py->line(267)|Client 5 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.03s, avg_attempt=0.028s
WARNING 2025-08-05 09:48:28   iaffbo_client.py->line(267)|Client 14 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.02s, avg_attempt=0.022s
INFO    2025-08-05 09:48:28   iaffbo_client.py->line(232)|Client 17 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:48:28   iaffbo_client.py->line(267)|Client 10 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.02s, avg_attempt=0.023s
WARNING 2025-08-05 09:48:28   iaffbo_client.py->line(267)|Client 17 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.03s, avg_attempt=0.011s
INFO    2025-08-05 09:48:28   iaffbo_client.py->line(232)|Client 6 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-05 09:48:28   iaffbo_client.py->line(232)|Client 13 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:48:28   iaffbo_client.py->line(267)|Client 6 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.02s, avg_attempt=0.012s
INFO    2025-08-05 09:48:28   iaffbo_client.py->line(232)|Client 8 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:48:28   iaffbo_client.py->line(267)|Client 8 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.007s
INFO    2025-08-05 09:48:28   iaffbo_client.py->line(232)|Client 18 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:48:28   iaffbo_client.py->line(267)|Client 13 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.02s, avg_attempt=0.018s
WARNING 2025-08-05 09:48:28   iaffbo_client.py->line(267)|Client 18 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.02s, avg_attempt=0.015s
INFO    2025-08-05 09:48:39   iaffbo_server.py->line(42)|Server received weights from Client 3, version=0
INFO    2025-08-05 09:48:39   iaffbo_server.py->line(42)|Server received weights from Client 2, version=0
INFO    2025-08-05 09:48:39   iaffbo_server.py->line(42)|Server received weights from Client 14, version=0
INFO    2025-08-05 09:48:39   iaffbo_server.py->line(42)|Server received weights from Client 9, version=0
INFO    2025-08-05 09:48:39   iaffbo_server.py->line(42)|Server received weights from Client 12, version=0
INFO    2025-08-05 09:48:39   iaffbo_server.py->line(42)|Server received weights from Client 1, version=0
INFO    2025-08-05 09:48:39   iaffbo_server.py->line(42)|Server received weights from Client 10, version=0
INFO    2025-08-05 09:48:39   iaffbo_server.py->line(42)|Server received weights from Client 15, version=0
INFO    2025-08-05 09:48:39   iaffbo_server.py->line(42)|Server received weights from Client 13, version=0
INFO    2025-08-05 09:48:39   iaffbo_server.py->line(42)|Server received weights from Client 11, version=0
INFO    2025-08-05 09:48:39   iaffbo_server.py->line(42)|Server received weights from Client 6, version=0
INFO    2025-08-05 09:48:39   iaffbo_server.py->line(42)|Server received weights from Client 8, version=0
INFO    2025-08-05 09:48:39   iaffbo_server.py->line(42)|Server received weights from Client 17, version=0
INFO    2025-08-05 09:48:39   iaffbo_server.py->line(42)|Server received weights from Client 7, version=0
INFO    2025-08-05 09:48:39   iaffbo_server.py->line(42)|Server received weights from Client 16, version=0
INFO    2025-08-05 09:48:39   iaffbo_server.py->line(42)|Server received weights from Client 18, version=0
INFO    2025-08-05 09:48:39   iaffbo_server.py->line(42)|Server received weights from Client 4, version=0
INFO    2025-08-05 09:48:39   iaffbo_server.py->line(42)|Server received weights from Client 5, version=0
INFO    2025-08-05 09:48:40          client.py->line(103)|
================================================ Client 3  Ackley(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.06s] │ [0.00s]           │ [0.01s]                   │ [0.01s] │ [10.62s]          │ [0.01s] │ [1.71s] │ [12.40s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:48:41   iaffbo_client.py->line(232)|Client 3 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:48:41   iaffbo_client.py->line(267)|Client 3 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.003s
INFO    2025-08-05 09:48:41          client.py->line(103)|
=============================================== Client 2  Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.08s] │ [0.01s]           │ [0.02s]                   │ [0.03s] │ [10.63s]          │ [0.00s] │ [1.77s] │ [12.51s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:48:41   iaffbo_client.py->line(232)|Client 2 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:48:41   iaffbo_client.py->line(267)|Client 2 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.002s
INFO    2025-08-05 09:48:41          client.py->line(103)|
================================================ Client 9  Ackley(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.09s] │ [0.01s]           │ [0.02s]                   │ [0.04s] │ [10.83s]          │ [0.01s] │ [2.04s] │ [13.00s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:48:41          client.py->line(103)|
=============================================== Client 14 Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.11s] │ [0.02s]           │ [0.03s]                   │ [0.05s] │ [10.79s]          │ [0.01s] │ [2.08s] │ [13.04s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:48:41          client.py->line(103)|
============================================== Client 12 Weierstrass(10D) ==============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.10s] │ [0.02s]           │ [0.03s]                   │ [0.05s] │ [10.85s]          │ [0.01s] │ [2.04s] │ [13.04s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:48:41          client.py->line(103)|
=============================================== Client 1  Griewank(10D) ================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.08s] │ [0.01s]           │ [0.01s]                   │ [0.02s] │ [10.97s]          │ [0.01s] │ [2.01s] │ [13.09s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:48:41          client.py->line(103)|
============================================== Client 10 Rosenbrock(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.13s] │ [0.03s]           │ [0.03s]                   │ [0.05s] │ [10.88s]          │ [0.00s] │ [2.01s] │ [13.07s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:48:41          client.py->line(103)|
=============================================== Client 15 Griewank(10D) ================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.08s] │ [0.02s]           │ [0.03s]                   │ [0.05s] │ [10.93s]          │ [0.00s] │ [2.02s] │ [13.09s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:48:41   iaffbo_client.py->line(232)|Client 15 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-05 09:48:41   iaffbo_client.py->line(232)|Client 9 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-05 09:48:41   iaffbo_client.py->line(232)|Client 12 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-05 09:48:41   iaffbo_client.py->line(232)|Client 1 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:48:41   iaffbo_client.py->line(267)|Client 15 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.007s
WARNING 2025-08-05 09:48:41   iaffbo_client.py->line(267)|Client 9 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.005s
WARNING 2025-08-05 09:48:41   iaffbo_client.py->line(267)|Client 1 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.008s
INFO    2025-08-05 09:48:41   iaffbo_client.py->line(232)|Client 14 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:48:41   iaffbo_client.py->line(267)|Client 12 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.02s, avg_attempt=0.017s
WARNING 2025-08-05 09:48:41   iaffbo_client.py->line(267)|Client 14 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.006s
INFO    2025-08-05 09:48:41   iaffbo_client.py->line(232)|Client 10 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:48:41   iaffbo_client.py->line(267)|Client 10 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.004s
INFO    2025-08-05 09:48:41          client.py->line(103)|
================================================ Client 8  Sphere(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.18s] │ [0.02s]           │ [0.01s]                   │ [0.03s] │ [10.92s]          │ [0.00s] │ [2.00s] │ [13.15s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:48:41          client.py->line(103)|
============================================== Client 13 Rosenbrock(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.19s] │ [0.02s]           │ [0.03s]                   │ [0.05s] │ [10.84s]          │ [0.00s] │ [2.12s] │ [13.21s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:48:41   iaffbo_client.py->line(232)|Client 8 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:48:41   iaffbo_client.py->line(267)|Client 8 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.009s
INFO    2025-08-05 09:48:41          client.py->line(103)|
=============================================== Client 17 Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.13s] │ [0.02s]           │ [0.03s]                   │ [0.05s] │ [10.96s]          │ [0.00s] │ [2.12s] │ [13.27s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:48:41          client.py->line(103)|
=============================================== Client 18 Schwefel(10D) ================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.21s] │ [0.01s]           │ [0.02s]                   │ [0.03s] │ [10.95s]          │ [0.00s] │ [2.08s] │ [13.27s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:48:41          client.py->line(103)|
=============================================== Client 7  Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.04s] │ [0.01s]           │ [0.01s]                   │ [0.01s] │ [11.12s]          │ [0.00s] │ [2.11s] │ [13.29s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:48:41          client.py->line(103)|
================================================ Client 11 Ackley(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.04s] │ [0.01s]           │ [0.02s]                   │ [0.03s] │ [11.04s]          │ [0.00s] │ [2.18s] │ [13.29s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:48:41          client.py->line(103)|
================================================ Client 5  Ackley(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.13s] │ [0.02s]           │ [0.03s]                   │ [0.06s] │ [11.05s]          │ [0.00s] │ [2.08s] │ [13.32s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:48:41   iaffbo_client.py->line(232)|Client 18 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-05 09:48:41   iaffbo_client.py->line(232)|Client 7 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-05 09:48:41   iaffbo_client.py->line(232)|Client 17 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:48:41   iaffbo_client.py->line(267)|Client 18 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.013s
WARNING 2025-08-05 09:48:41   iaffbo_client.py->line(267)|Client 7 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.011s
INFO    2025-08-05 09:48:41          client.py->line(103)|
=============================================== Client 4  Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.08s] │ [0.02s]           │ [0.02s]                   │ [0.04s] │ [11.09s]          │ [0.00s] │ [2.15s] │ [13.36s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
WARNING 2025-08-05 09:48:41   iaffbo_client.py->line(267)|Client 17 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.02s, avg_attempt=0.019s
INFO    2025-08-05 09:48:41          client.py->line(103)|
=============================================== Client 6  Schwefel(10D) ================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.19s] │ [0.02s]           │ [0.02s]                   │ [0.03s] │ [10.91s]          │ [0.00s] │ [2.23s] │ [13.37s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:48:41          client.py->line(103)|
============================================== Client 16 Weierstrass(10D) ==============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.05s] │ [0.01s]           │ [0.01s]                   │ [0.02s] │ [11.11s]          │ [0.00s] │ [2.19s] │ [13.37s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:48:41   iaffbo_client.py->line(232)|Client 13 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:48:41   iaffbo_client.py->line(267)|Client 13 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.005s
INFO    2025-08-05 09:48:42   iaffbo_client.py->line(232)|Client 5 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:48:42   iaffbo_client.py->line(267)|Client 5 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.005s
INFO    2025-08-05 09:48:42   iaffbo_client.py->line(232)|Client 11 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:48:42   iaffbo_client.py->line(267)|Client 11 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.009s
INFO    2025-08-05 09:48:42   iaffbo_client.py->line(232)|Client 16 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:48:42   iaffbo_client.py->line(267)|Client 16 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.02s, avg_attempt=0.021s
INFO    2025-08-05 09:48:42   iaffbo_client.py->line(232)|Client 6 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-05 09:48:42   iaffbo_client.py->line(232)|Client 4 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:48:42   iaffbo_client.py->line(267)|Client 6 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.009s
WARNING 2025-08-05 09:48:42   iaffbo_client.py->line(267)|Client 4 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.009s
INFO    2025-08-05 09:48:50   iaffbo_server.py->line(42)|Server received weights from Client 3, version=1
INFO    2025-08-05 09:48:51   iaffbo_server.py->line(42)|Server received weights from Client 2, version=1
INFO    2025-08-05 09:48:52   iaffbo_server.py->line(42)|Server received weights from Client 10, version=1
INFO    2025-08-05 09:48:52   iaffbo_server.py->line(42)|Server received weights from Client 12, version=1
INFO    2025-08-05 09:48:52   iaffbo_server.py->line(42)|Server received weights from Client 15, version=1
INFO    2025-08-05 09:48:52          client.py->line(103)|
================================================ Client 3  Ackley(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.09s] │ [0.00s]           │ [0.00s]                   │ [0.00s] │ [9.80s]           │ [0.01s] │ [1.36s] │ [11.26s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:48:52   iaffbo_server.py->line(42)|Server received weights from Client 9, version=1
INFO    2025-08-05 09:48:52   iaffbo_server.py->line(42)|Server received weights from Client 14, version=1
INFO    2025-08-05 09:48:52   iaffbo_server.py->line(42)|Server received weights from Client 5, version=1
INFO    2025-08-05 09:48:52   iaffbo_server.py->line(42)|Server received weights from Client 1, version=1
INFO    2025-08-05 09:48:52   iaffbo_server.py->line(42)|Server received weights from Client 4, version=1
INFO    2025-08-05 09:48:52   iaffbo_client.py->line(232)|Client 3 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:48:52   iaffbo_client.py->line(267)|Client 3 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.007s
INFO    2025-08-05 09:48:52   iaffbo_server.py->line(42)|Server received weights from Client 11, version=1
INFO    2025-08-05 09:48:52   iaffbo_server.py->line(42)|Server received weights from Client 8, version=1
INFO    2025-08-05 09:48:52   iaffbo_server.py->line(42)|Server received weights from Client 7, version=1
INFO    2025-08-05 09:48:52   iaffbo_server.py->line(42)|Server received weights from Client 17, version=1
INFO    2025-08-05 09:48:52   iaffbo_server.py->line(42)|Server received weights from Client 16, version=1
INFO    2025-08-05 09:48:52   iaffbo_server.py->line(42)|Server received weights from Client 18, version=1
INFO    2025-08-05 09:48:52   iaffbo_server.py->line(42)|Server received weights from Client 13, version=1
INFO    2025-08-05 09:48:52          client.py->line(103)|
=============================================== Client 2  Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.11s] │ [0.01s]           │ [0.00s]                   │ [0.00s] │ [9.85s]           │ [0.00s] │ [1.49s] │ [11.46s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:48:52   iaffbo_server.py->line(42)|Server received weights from Client 6, version=1
INFO    2025-08-05 09:48:52   iaffbo_client.py->line(232)|Client 2 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:48:52   iaffbo_client.py->line(267)|Client 2 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.004s
INFO    2025-08-05 09:48:54          client.py->line(103)|
============================================== Client 10 Rosenbrock(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.08s] │ [0.03s]           │ [0.00s]                   │ [0.00s] │ [10.37s]          │ [0.00s] │ [2.29s] │ [12.74s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:48:54   iaffbo_client.py->line(232)|Client 10 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:48:54   iaffbo_client.py->line(267)|Client 10 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.004s
INFO    2025-08-05 09:48:54          client.py->line(103)|
============================================== Client 12 Weierstrass(10D) ==============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.07s] │ [0.02s]           │ [0.02s]                   │ [0.02s] │ [10.42s]          │ [0.00s] │ [2.40s] │ [12.90s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:48:54          client.py->line(103)|
=============================================== Client 15 Griewank(10D) ================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.02s] │ [0.02s]           │ [0.01s]                   │ [0.01s] │ [10.50s]          │ [0.00s] │ [2.39s] │ [12.92s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:48:54   iaffbo_client.py->line(232)|Client 12 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:48:54   iaffbo_client.py->line(267)|Client 12 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.005s
INFO    2025-08-05 09:48:54   iaffbo_client.py->line(232)|Client 15 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:48:54   iaffbo_client.py->line(267)|Client 15 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.005s
INFO    2025-08-05 09:48:54          client.py->line(103)|
=============================================== Client 14 Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.09s] │ [0.02s]           │ [0.01s]                   │ [0.01s] │ [10.58s]          │ [0.01s] │ [2.52s] │ [13.21s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:48:54          client.py->line(103)|
================================================ Client 5  Ackley(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.09s] │ [0.02s]           │ [0.01s]                   │ [0.01s] │ [10.31s]          │ [0.01s] │ [2.55s] │ [12.97s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:48:54          client.py->line(103)|
=============================================== Client 1  Griewank(10D) ================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.06s] │ [0.01s]           │ [0.01s]                   │ [0.01s] │ [10.62s]          │ [0.01s] │ [2.56s] │ [13.26s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:48:54          client.py->line(103)|
================================================ Client 11 Ackley(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.14s] │ [0.01s]           │ [0.01s]                   │ [0.01s] │ [10.34s]          │ [0.00s] │ [2.53s] │ [13.03s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:48:54          client.py->line(103)|
=============================================== Client 17 Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.06s] │ [0.02s]           │ [0.02s]                   │ [0.02s] │ [10.44s]          │ [0.01s] │ [2.51s] │ [13.04s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:48:54          client.py->line(103)|
=============================================== Client 4  Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.16s] │ [0.02s]           │ [0.01s]                   │ [0.01s] │ [10.23s]          │ [0.00s] │ [2.57s] │ [12.98s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:48:54          client.py->line(103)|
================================================ Client 8  Sphere(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.11s] │ [0.02s]           │ [0.01s]                   │ [0.01s] │ [10.53s]          │ [0.01s] │ [2.54s] │ [13.19s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:48:54          client.py->line(103)|
=============================================== Client 7  Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.05s] │ [0.01s]           │ [0.01s]                   │ [0.01s] │ [10.45s]          │ [0.01s] │ [2.55s] │ [13.07s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:48:54          client.py->line(103)|
================================================ Client 9  Ackley(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.12s] │ [0.01s]           │ [0.01s]                   │ [0.01s] │ [10.59s]          │ [0.01s] │ [2.64s] │ [13.37s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:48:54   iaffbo_client.py->line(232)|Client 14 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:48:54   iaffbo_client.py->line(267)|Client 14 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.005s
INFO    2025-08-05 09:48:54          client.py->line(103)|
============================================== Client 16 Weierstrass(10D) ==============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.10s] │ [0.01s]           │ [0.02s]                   │ [0.02s] │ [10.38s]          │ [0.00s] │ [2.49s] │ [13.00s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:48:55          client.py->line(103)|
=============================================== Client 18 Schwefel(10D) ================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.05s] │ [0.01s]           │ [0.01s]                   │ [0.01s] │ [10.54s]          │ [0.00s] │ [2.51s] │ [13.12s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:48:55   iaffbo_client.py->line(232)|Client 11 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:48:55   iaffbo_client.py->line(267)|Client 11 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.02s, avg_attempt=0.016s
INFO    2025-08-05 09:48:55   iaffbo_client.py->line(232)|Client 5 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-05 09:48:55   iaffbo_client.py->line(232)|Client 4 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-05 09:48:55          client.py->line(103)|
============================================== Client 13 Rosenbrock(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.18s] │ [0.02s]           │ [0.01s]                   │ [0.01s] │ [10.53s]          │ [0.00s] │ [2.51s] │ [13.23s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:48:55   iaffbo_client.py->line(232)|Client 17 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-05 09:48:55   iaffbo_client.py->line(232)|Client 16 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-05 09:48:55   iaffbo_client.py->line(232)|Client 8 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:48:55   iaffbo_client.py->line(267)|Client 5 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.03s, avg_attempt=0.008s
INFO    2025-08-05 09:48:55   iaffbo_client.py->line(232)|Client 9 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:48:55   iaffbo_client.py->line(267)|Client 4 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.007s
WARNING 2025-08-05 09:48:55   iaffbo_client.py->line(267)|Client 16 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.007s
WARNING 2025-08-05 09:48:55   iaffbo_client.py->line(267)|Client 17 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.02s, avg_attempt=0.018s
WARNING 2025-08-05 09:48:55   iaffbo_client.py->line(267)|Client 8 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.02s, avg_attempt=0.012s
WARNING 2025-08-05 09:48:55   iaffbo_client.py->line(267)|Client 9 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.02s, avg_attempt=0.014s
INFO    2025-08-05 09:48:55   iaffbo_client.py->line(232)|Client 1 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:48:55   iaffbo_client.py->line(267)|Client 1 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.02s, avg_attempt=0.016s
INFO    2025-08-05 09:48:55   iaffbo_client.py->line(232)|Client 7 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-05 09:48:55   iaffbo_client.py->line(232)|Client 18 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:48:55   iaffbo_client.py->line(267)|Client 7 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.02s, avg_attempt=0.014s
WARNING 2025-08-05 09:48:55   iaffbo_client.py->line(267)|Client 18 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.03s, avg_attempt=0.020s
INFO    2025-08-05 09:48:55   iaffbo_client.py->line(232)|Client 13 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-05 09:48:55          client.py->line(103)|
=============================================== Client 6  Schwefel(10D) ================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.15s] │ [0.02s]           │ [0.01s]                   │ [0.01s] │ [10.44s]          │ [0.01s] │ [2.58s] │ [13.19s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
WARNING 2025-08-05 09:48:55   iaffbo_client.py->line(267)|Client 13 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.011s
INFO    2025-08-05 09:48:55   iaffbo_client.py->line(232)|Client 6 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:48:55   iaffbo_client.py->line(267)|Client 6 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.009s
INFO    2025-08-05 09:49:00   iaffbo_server.py->line(42)|Server received weights from Client 3, version=2
INFO    2025-08-05 09:49:00   iaffbo_server.py->line(42)|Server received weights from Client 2, version=2
INFO    2025-08-05 09:49:01          client.py->line(103)|
================================================ Client 3  Ackley(10D) ================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬─────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total   │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼─────────┤
│ [0.13s] │ [0.00s]           │ [0.01s]                   │ [0.01s] │ [7.78s]           │ [0.01s] │ [1.35s] │ [9.28s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴─────────╯
INFO    2025-08-05 09:49:01   iaffbo_client.py->line(232)|Client 3 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:01   iaffbo_client.py->line(267)|Client 3 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.005s
INFO    2025-08-05 09:49:01          client.py->line(103)|
=============================================== Client 2  Rastrigin(10D) ==============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬─────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total   │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼─────────┤
│ [0.11s] │ [0.01s]           │ [0.00s]                   │ [0.00s] │ [7.75s]           │ [0.00s] │ [1.41s] │ [9.27s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴─────────╯
INFO    2025-08-05 09:49:01   iaffbo_client.py->line(232)|Client 2 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:01   iaffbo_client.py->line(267)|Client 2 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.006s
INFO    2025-08-05 09:49:04   iaffbo_server.py->line(42)|Server received weights from Client 10, version=2
INFO    2025-08-05 09:49:05   iaffbo_server.py->line(42)|Server received weights from Client 12, version=2
INFO    2025-08-05 09:49:05   iaffbo_server.py->line(42)|Server received weights from Client 15, version=2
INFO    2025-08-05 09:49:05   iaffbo_server.py->line(42)|Server received weights from Client 11, version=2
INFO    2025-08-05 09:49:05   iaffbo_server.py->line(42)|Server received weights from Client 9, version=2
INFO    2025-08-05 09:49:05   iaffbo_server.py->line(42)|Server received weights from Client 8, version=2
INFO    2025-08-05 09:49:05   iaffbo_server.py->line(42)|Server received weights from Client 4, version=2
INFO    2025-08-05 09:49:05   iaffbo_server.py->line(42)|Server received weights from Client 17, version=2
INFO    2025-08-05 09:49:05   iaffbo_server.py->line(42)|Server received weights from Client 13, version=2
INFO    2025-08-05 09:49:05   iaffbo_server.py->line(42)|Server received weights from Client 7, version=2
INFO    2025-08-05 09:49:05   iaffbo_server.py->line(42)|Server received weights from Client 5, version=2
INFO    2025-08-05 09:49:05   iaffbo_server.py->line(42)|Server received weights from Client 14, version=2
INFO    2025-08-05 09:49:05   iaffbo_server.py->line(42)|Server received weights from Client 16, version=2
INFO    2025-08-05 09:49:05   iaffbo_server.py->line(42)|Server received weights from Client 1, version=2
INFO    2025-08-05 09:49:05   iaffbo_server.py->line(42)|Server received weights from Client 6, version=2
INFO    2025-08-05 09:49:05   iaffbo_server.py->line(42)|Server received weights from Client 18, version=2
INFO    2025-08-05 09:49:06          client.py->line(103)|
============================================== Client 10 Rosenbrock(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.10s] │ [0.03s]           │ [0.01s]                   │ [0.01s] │ [10.31s]          │ [0.01s] │ [1.80s] │ [12.23s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:06   iaffbo_client.py->line(232)|Client 10 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:06   iaffbo_client.py->line(267)|Client 10 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.003s
INFO    2025-08-05 09:49:07          client.py->line(103)|
============================================== Client 12 Weierstrass(10D) ==============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.10s] │ [0.02s]           │ [0.01s]                   │ [0.01s] │ [10.54s]          │ [0.01s] │ [2.18s] │ [12.83s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:07   iaffbo_client.py->line(232)|Client 12 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:07   iaffbo_client.py->line(267)|Client 12 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.003s
INFO    2025-08-05 09:49:07          client.py->line(103)|
=============================================== Client 15 Griewank(10D) ================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.12s] │ [0.02s]           │ [0.01s]                   │ [0.01s] │ [10.55s]          │ [0.01s] │ [2.30s] │ [12.99s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:07   iaffbo_client.py->line(232)|Client 15 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:07   iaffbo_client.py->line(267)|Client 15 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.003s
INFO    2025-08-05 09:49:07          client.py->line(103)|
================================================ Client 11 Ackley(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.08s] │ [0.01s]           │ [0.02s]                   │ [0.02s] │ [10.43s]          │ [0.01s] │ [2.44s] │ [12.98s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:08   iaffbo_client.py->line(232)|Client 11 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:08   iaffbo_client.py->line(267)|Client 11 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.005s
INFO    2025-08-05 09:49:08          client.py->line(103)|
================================================ Client 9  Ackley(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.09s] │ [0.01s]           │ [0.02s]                   │ [0.02s] │ [10.44s]          │ [0.01s] │ [2.55s] │ [13.11s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:08   iaffbo_client.py->line(232)|Client 9 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:08   iaffbo_client.py->line(267)|Client 9 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.003s
INFO    2025-08-05 09:49:08          client.py->line(103)|
================================================ Client 8  Sphere(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.11s] │ [0.02s]           │ [0.03s]                   │ [0.03s] │ [10.51s]          │ [0.01s] │ [2.65s] │ [13.30s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:08          client.py->line(103)|
=============================================== Client 17 Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.13s] │ [0.02s]           │ [0.02s]                   │ [0.02s] │ [10.56s]          │ [0.01s] │ [2.66s] │ [13.37s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:08          client.py->line(103)|
================================================ Client 5  Ackley(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.14s] │ [0.02s]           │ [0.04s]                   │ [0.04s] │ [10.61s]          │ [0.01s] │ [2.62s] │ [13.41s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:08          client.py->line(103)|
=============================================== Client 7  Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.15s] │ [0.01s]           │ [0.02s]                   │ [0.02s] │ [10.54s]          │ [0.00s] │ [2.67s] │ [13.38s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:08          client.py->line(103)|
=============================================== Client 4  Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.11s] │ [0.02s]           │ [0.02s]                   │ [0.02s] │ [10.53s]          │ [0.01s] │ [2.76s] │ [13.42s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:08   iaffbo_client.py->line(232)|Client 8 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-05 09:49:08          client.py->line(103)|
=============================================== Client 1  Griewank(10D) ================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.17s] │ [0.01s]           │ [0.02s]                   │ [0.02s] │ [10.62s]          │ [0.00s] │ [2.66s] │ [13.47s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
WARNING 2025-08-05 09:49:08   iaffbo_client.py->line(267)|Client 8 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.02s, avg_attempt=0.020s
INFO    2025-08-05 09:49:08   iaffbo_client.py->line(232)|Client 4 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-05 09:49:08   iaffbo_client.py->line(232)|Client 5 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:08   iaffbo_client.py->line(267)|Client 4 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.008s
WARNING 2025-08-05 09:49:08   iaffbo_client.py->line(267)|Client 5 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.009s
INFO    2025-08-05 09:49:08          client.py->line(103)|
=============================================== Client 14 Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.12s] │ [0.02s]           │ [0.01s]                   │ [0.01s] │ [10.74s]          │ [0.01s] │ [2.71s] │ [13.57s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:08          client.py->line(103)|
============================================== Client 13 Rosenbrock(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.10s] │ [0.02s]           │ [0.01s]                   │ [0.01s] │ [10.49s]          │ [0.00s] │ [2.77s] │ [13.37s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:08   iaffbo_client.py->line(232)|Client 17 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-05 09:49:08          client.py->line(103)|
=============================================== Client 6  Schwefel(10D) ================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.12s] │ [0.02s]           │ [0.01s]                   │ [0.01s] │ [10.44s]          │ [0.01s] │ [2.69s] │ [13.27s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
WARNING 2025-08-05 09:49:08   iaffbo_client.py->line(267)|Client 17 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.011s
INFO    2025-08-05 09:49:08   iaffbo_client.py->line(232)|Client 7 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-05 09:49:08          client.py->line(103)|
============================================== Client 16 Weierstrass(10D) ==============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.08s] │ [0.01s]           │ [0.02s]                   │ [0.02s] │ [10.65s]          │ [0.00s] │ [2.74s] │ [13.49s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
WARNING 2025-08-05 09:49:08   iaffbo_client.py->line(267)|Client 7 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.013s
INFO    2025-08-05 09:49:08          client.py->line(103)|
=============================================== Client 18 Schwefel(10D) ================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.11s] │ [0.01s]           │ [0.03s]                   │ [0.03s] │ [10.64s]          │ [0.00s] │ [2.70s] │ [13.49s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:08   iaffbo_client.py->line(232)|Client 1 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:08   iaffbo_client.py->line(267)|Client 1 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.007s
INFO    2025-08-05 09:49:08   iaffbo_client.py->line(232)|Client 13 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:08   iaffbo_client.py->line(267)|Client 13 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.009s
INFO    2025-08-05 09:49:08   iaffbo_client.py->line(232)|Client 14 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:08   iaffbo_client.py->line(267)|Client 14 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.008s
INFO    2025-08-05 09:49:08   iaffbo_client.py->line(232)|Client 18 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-05 09:49:08   iaffbo_client.py->line(232)|Client 16 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:08   iaffbo_client.py->line(267)|Client 18 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.014s
INFO    2025-08-05 09:49:08   iaffbo_client.py->line(232)|Client 6 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:08   iaffbo_client.py->line(267)|Client 16 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.005s
WARNING 2025-08-05 09:49:08   iaffbo_client.py->line(267)|Client 6 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.006s
INFO    2025-08-05 09:49:09   iaffbo_server.py->line(42)|Server received weights from Client 3, version=3
INFO    2025-08-05 09:49:09   iaffbo_server.py->line(42)|Server received weights from Client 2, version=3
INFO    2025-08-05 09:49:11          client.py->line(103)|
================================================ Client 3  Ackley(10D) ================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬─────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total   │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼─────────┤
│ [0.16s] │ [0.00s]           │ [0.01s]                   │ [0.01s] │ [7.94s]           │ [0.00s] │ [1.39s] │ [9.50s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴─────────╯
INFO    2025-08-05 09:49:11   iaffbo_client.py->line(232)|Client 3 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:11   iaffbo_client.py->line(267)|Client 3 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.005s
INFO    2025-08-05 09:49:11          client.py->line(103)|
=============================================== Client 2  Rastrigin(10D) ==============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬─────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total   │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼─────────┤
│ [0.15s] │ [0.01s]           │ [0.01s]                   │ [0.01s] │ [7.85s]           │ [0.01s] │ [1.44s] │ [9.45s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴─────────╯
INFO    2025-08-05 09:49:11   iaffbo_client.py->line(232)|Client 2 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:11   iaffbo_client.py->line(267)|Client 2 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.003s
INFO    2025-08-05 09:49:16   iaffbo_server.py->line(42)|Server received weights from Client 10, version=3
INFO    2025-08-05 09:49:17          client.py->line(103)|
============================================== Client 10 Rosenbrock(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.10s] │ [0.03s]           │ [0.00s]                   │ [0.00s] │ [9.33s]           │ [0.01s] │ [1.27s] │ [10.72s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:17   iaffbo_client.py->line(232)|Client 10 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:17   iaffbo_client.py->line(267)|Client 10 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.006s
INFO    2025-08-05 09:49:17   iaffbo_server.py->line(42)|Server received weights from Client 12, version=3
INFO    2025-08-05 09:49:18   iaffbo_server.py->line(42)|Server received weights from Client 15, version=3
INFO    2025-08-05 09:49:18   iaffbo_server.py->line(42)|Server received weights from Client 11, version=3
INFO    2025-08-05 09:49:18   iaffbo_server.py->line(42)|Server received weights from Client 8, version=3
INFO    2025-08-05 09:49:18   iaffbo_server.py->line(42)|Server received weights from Client 4, version=3
INFO    2025-08-05 09:49:19   iaffbo_server.py->line(42)|Server received weights from Client 9, version=3
INFO    2025-08-05 09:49:19   iaffbo_server.py->line(42)|Server received weights from Client 17, version=3
INFO    2025-08-05 09:49:19   iaffbo_server.py->line(42)|Server received weights from Client 7, version=3
INFO    2025-08-05 09:49:19   iaffbo_server.py->line(42)|Server received weights from Client 5, version=3
INFO    2025-08-05 09:49:19   iaffbo_server.py->line(42)|Server received weights from Client 1, version=3
INFO    2025-08-05 09:49:19   iaffbo_server.py->line(42)|Server received weights from Client 16, version=3
INFO    2025-08-05 09:49:19   iaffbo_server.py->line(42)|Server received weights from Client 13, version=3
INFO    2025-08-05 09:49:19   iaffbo_server.py->line(42)|Server received weights from Client 18, version=3
INFO    2025-08-05 09:49:19          client.py->line(103)|
============================================== Client 12 Weierstrass(10D) ==============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.08s] │ [0.02s]           │ [0.01s]                   │ [0.01s] │ [10.27s]          │ [0.00s] │ [1.38s] │ [11.74s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:19   iaffbo_server.py->line(42)|Server received weights from Client 14, version=3
INFO    2025-08-05 09:49:19   iaffbo_server.py->line(42)|Server received weights from Client 6, version=3
INFO    2025-08-05 09:49:19   iaffbo_client.py->line(232)|Client 12 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:19   iaffbo_client.py->line(267)|Client 12 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.004s
INFO    2025-08-05 09:49:19          client.py->line(103)|
=============================================== Client 15 Griewank(10D) ================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.11s] │ [0.02s]           │ [0.00s]                   │ [0.00s] │ [10.36s]          │ [0.01s] │ [1.90s] │ [12.38s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:20   iaffbo_client.py->line(232)|Client 15 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:20   iaffbo_client.py->line(267)|Client 15 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.004s
INFO    2025-08-05 09:49:20   iaffbo_server.py->line(42)|Server received weights from Client 3, version=4
INFO    2025-08-05 09:49:20   iaffbo_server.py->line(42)|Server received weights from Client 2, version=4
INFO    2025-08-05 09:49:20          client.py->line(103)|
================================================ Client 11 Ackley(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.10s] │ [0.01s]           │ [0.01s]                   │ [0.01s] │ [10.54s]          │ [0.01s] │ [2.35s] │ [13.00s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:21   iaffbo_client.py->line(232)|Client 11 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:21   iaffbo_client.py->line(267)|Client 11 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.003s
INFO    2025-08-05 09:49:21          client.py->line(103)|
================================================ Client 8  Sphere(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.14s] │ [0.02s]           │ [0.02s]                   │ [0.02s] │ [10.48s]          │ [0.01s] │ [2.80s] │ [13.45s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:21          client.py->line(103)|
================================================ Client 9  Ackley(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.11s] │ [0.01s]           │ [0.01s]                   │ [0.01s] │ [10.80s]          │ [0.01s] │ [2.76s] │ [13.68s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:21          client.py->line(103)|
=============================================== Client 4  Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.05s] │ [0.02s]           │ [0.01s]                   │ [0.01s] │ [10.54s]          │ [0.01s] │ [2.80s] │ [13.41s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:21   iaffbo_client.py->line(232)|Client 8 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:21   iaffbo_client.py->line(267)|Client 8 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.005s
INFO    2025-08-05 09:49:21          client.py->line(103)|
=============================================== Client 17 Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.14s] │ [0.02s]           │ [0.01s]                   │ [0.01s] │ [10.55s]          │ [0.00s] │ [2.83s] │ [13.54s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:21   iaffbo_client.py->line(232)|Client 9 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-05 09:49:21          client.py->line(103)|
=============================================== Client 7  Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.11s] │ [0.01s]           │ [0.02s]                   │ [0.02s] │ [10.55s]          │ [0.01s] │ [2.84s] │ [13.53s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
WARNING 2025-08-05 09:49:21   iaffbo_client.py->line(267)|Client 9 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.005s
INFO    2025-08-05 09:49:21          client.py->line(103)|
================================================ Client 5  Ackley(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.11s] │ [0.02s]           │ [0.02s]                   │ [0.02s] │ [10.61s]          │ [0.01s] │ [2.85s] │ [13.60s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:21   iaffbo_client.py->line(232)|Client 17 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-05 09:49:21          client.py->line(103)|
=============================================== Client 1  Griewank(10D) ================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.11s] │ [0.01s]           │ [0.01s]                   │ [0.01s] │ [10.55s]          │ [0.00s] │ [2.87s] │ [13.54s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:21   iaffbo_client.py->line(232)|Client 4 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:21   iaffbo_client.py->line(267)|Client 17 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.007s
WARNING 2025-08-05 09:49:21   iaffbo_client.py->line(267)|Client 4 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.007s
INFO    2025-08-05 09:49:21   iaffbo_client.py->line(232)|Client 7 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-05 09:49:21          client.py->line(103)|
============================================== Client 13 Rosenbrock(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.09s] │ [0.02s]           │ [0.02s]                   │ [0.02s] │ [10.55s]          │ [0.01s] │ [2.86s] │ [13.52s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
WARNING 2025-08-05 09:49:21   iaffbo_client.py->line(267)|Client 7 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.011s
INFO    2025-08-05 09:49:21   iaffbo_client.py->line(232)|Client 5 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:22   iaffbo_client.py->line(267)|Client 5 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.005s
INFO    2025-08-05 09:49:22   iaffbo_client.py->line(232)|Client 13 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:22   iaffbo_client.py->line(267)|Client 13 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.005s
INFO    2025-08-05 09:49:22          client.py->line(103)|
=============================================== Client 18 Schwefel(10D) ================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.12s] │ [0.01s]           │ [0.01s]                   │ [0.01s] │ [10.48s]          │ [0.01s] │ [2.94s] │ [13.56s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:22   iaffbo_client.py->line(232)|Client 1 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:22   iaffbo_client.py->line(267)|Client 1 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.005s
INFO    2025-08-05 09:49:22          client.py->line(103)|
=============================================== Client 14 Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.13s] │ [0.02s]           │ [0.01s]                   │ [0.01s] │ [10.61s]          │ [0.00s] │ [2.91s] │ [13.66s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:22          client.py->line(103)|
============================================== Client 16 Weierstrass(10D) ==============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.15s] │ [0.01s]           │ [0.01s]                   │ [0.01s] │ [10.44s]          │ [0.00s] │ [3.02s] │ [13.62s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:22          client.py->line(103)|
=============================================== Client 6  Schwefel(10D) ================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.19s] │ [0.02s]           │ [0.01s]                   │ [0.01s] │ [10.55s]          │ [0.00s] │ [2.92s] │ [13.67s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:22   iaffbo_client.py->line(232)|Client 18 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:22   iaffbo_client.py->line(267)|Client 18 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.007s
INFO    2025-08-05 09:49:22   iaffbo_client.py->line(232)|Client 16 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:22   iaffbo_client.py->line(267)|Client 16 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.007s
INFO    2025-08-05 09:49:22   iaffbo_client.py->line(232)|Client 14 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:22   iaffbo_client.py->line(267)|Client 14 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.02s, avg_attempt=0.013s
INFO    2025-08-05 09:49:22   iaffbo_client.py->line(232)|Client 6 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:22   iaffbo_client.py->line(267)|Client 6 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.006s
INFO    2025-08-05 09:49:22          client.py->line(103)|
================================================ Client 3  Ackley(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.14s] │ [0.00s]           │ [0.01s]                   │ [0.01s] │ [9.28s]           │ [0.00s] │ [2.34s] │ [11.77s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:22          client.py->line(103)|
=============================================== Client 2  Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.18s] │ [0.01s]           │ [0.00s]                   │ [0.00s] │ [9.07s]           │ [0.00s] │ [2.35s] │ [11.61s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:22   iaffbo_client.py->line(232)|Client 3 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:22   iaffbo_client.py->line(267)|Client 3 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.007s
INFO    2025-08-05 09:49:23   iaffbo_client.py->line(232)|Client 2 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:23   iaffbo_client.py->line(267)|Client 2 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.005s
INFO    2025-08-05 09:49:25   iaffbo_server.py->line(42)|Server received weights from Client 10, version=4
INFO    2025-08-05 09:49:27          client.py->line(103)|
============================================== Client 10 Rosenbrock(10D) ==============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬─────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total   │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼─────────┤
│ [0.14s] │ [0.03s]           │ [0.01s]                   │ [0.01s] │ [8.23s]           │ [0.01s] │ [1.33s] │ [9.71s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴─────────╯
INFO    2025-08-05 09:49:27   iaffbo_client.py->line(232)|Client 10 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:27   iaffbo_client.py->line(267)|Client 10 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.007s
INFO    2025-08-05 09:49:27   iaffbo_server.py->line(42)|Server received weights from Client 12, version=4
INFO    2025-08-05 09:49:29          client.py->line(103)|
============================================== Client 12 Weierstrass(10D) =============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬─────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total   │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼─────────┤
│ [0.08s] │ [0.02s]           │ [0.00s]                   │ [0.00s] │ [8.36s]           │ [0.00s] │ [1.42s] │ [9.87s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴─────────╯
INFO    2025-08-05 09:49:29   iaffbo_client.py->line(232)|Client 12 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:29   iaffbo_client.py->line(267)|Client 12 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.005s
INFO    2025-08-05 09:49:29   iaffbo_server.py->line(42)|Server received weights from Client 15, version=4
INFO    2025-08-05 09:49:31          client.py->line(103)|
=============================================== Client 15 Griewank(10D) ================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.10s] │ [0.02s]           │ [0.00s]                   │ [0.00s] │ [9.48s]           │ [0.01s] │ [1.44s] │ [11.03s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:31   iaffbo_client.py->line(232)|Client 15 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:31   iaffbo_client.py->line(267)|Client 15 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.010s
INFO    2025-08-05 09:49:31   iaffbo_server.py->line(42)|Server received weights from Client 11, version=4
INFO    2025-08-05 09:49:32          client.py->line(103)|
================================================ Client 11 Ackley(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.10s] │ [0.01s]           │ [0.00s]                   │ [0.00s] │ [10.46s]          │ [0.00s] │ [1.36s] │ [11.93s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:32   iaffbo_client.py->line(232)|Client 11 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:32   iaffbo_client.py->line(267)|Client 11 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.005s
INFO    2025-08-05 09:49:32   iaffbo_server.py->line(42)|Server received weights from Client 5, version=4
INFO    2025-08-05 09:49:33   iaffbo_server.py->line(42)|Server received weights from Client 8, version=4
INFO    2025-08-05 09:49:33   iaffbo_server.py->line(42)|Server received weights from Client 9, version=4
INFO    2025-08-05 09:49:33   iaffbo_server.py->line(42)|Server received weights from Client 7, version=4
INFO    2025-08-05 09:49:33   iaffbo_server.py->line(42)|Server received weights from Client 13, version=4
INFO    2025-08-05 09:49:33   iaffbo_server.py->line(42)|Server received weights from Client 4, version=4
INFO    2025-08-05 09:49:33   iaffbo_server.py->line(42)|Server received weights from Client 17, version=4
INFO    2025-08-05 09:49:33   iaffbo_server.py->line(42)|Server received weights from Client 1, version=4
INFO    2025-08-05 09:49:33   iaffbo_server.py->line(42)|Server received weights from Client 6, version=4
INFO    2025-08-05 09:49:33   iaffbo_server.py->line(42)|Server received weights from Client 18, version=4
INFO    2025-08-05 09:49:33   iaffbo_server.py->line(42)|Server received weights from Client 14, version=4
INFO    2025-08-05 09:49:33   iaffbo_server.py->line(42)|Server received weights from Client 16, version=4
INFO    2025-08-05 09:49:33   iaffbo_server.py->line(42)|Server received weights from Client 3, version=5
INFO    2025-08-05 09:49:33   iaffbo_server.py->line(42)|Server received weights from Client 2, version=5
INFO    2025-08-05 09:49:35          client.py->line(103)|
================================================ Client 5  Ackley(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.09s] │ [0.02s]           │ [0.01s]                   │ [0.01s] │ [10.97s]          │ [0.01s] │ [2.64s] │ [13.72s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:35   iaffbo_client.py->line(232)|Client 5 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:35   iaffbo_client.py->line(267)|Client 5 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.005s
INFO    2025-08-05 09:49:35          client.py->line(103)|
================================================ Client 8  Sphere(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.12s] │ [0.02s]           │ [0.01s]                   │ [0.01s] │ [11.27s]          │ [0.00s] │ [2.75s] │ [14.15s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:35   iaffbo_client.py->line(232)|Client 8 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:35   iaffbo_client.py->line(267)|Client 8 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.004s
INFO    2025-08-05 09:49:36          client.py->line(103)|
=============================================== Client 7  Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.07s] │ [0.01s]           │ [0.01s]                   │ [0.01s] │ [11.19s]          │ [0.01s] │ [2.85s] │ [14.13s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:36          client.py->line(103)|
================================================ Client 9  Ackley(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.12s] │ [0.01s]           │ [0.01s]                   │ [0.01s] │ [11.26s]          │ [0.01s] │ [2.87s] │ [14.26s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:36   iaffbo_server.py->line(42)|Server received weights from Client 10, version=5
INFO    2025-08-05 09:49:36          client.py->line(103)|
============================================== Client 13 Rosenbrock(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.05s] │ [0.02s]           │ [0.01s]                   │ [0.01s] │ [11.17s]          │ [0.01s] │ [2.85s] │ [14.09s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:36   iaffbo_client.py->line(232)|Client 9 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-05 09:49:36   iaffbo_client.py->line(232)|Client 7 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:36   iaffbo_client.py->line(267)|Client 9 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.004s
WARNING 2025-08-05 09:49:36   iaffbo_client.py->line(267)|Client 7 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.006s
INFO    2025-08-05 09:49:36   iaffbo_client.py->line(232)|Client 13 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:36   iaffbo_client.py->line(267)|Client 13 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.004s
INFO    2025-08-05 09:49:36          client.py->line(103)|
=============================================== Client 6  Schwefel(10D) ================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.18s] │ [0.02s]           │ [0.01s]                   │ [0.01s] │ [11.01s]          │ [0.01s] │ [2.93s] │ [14.13s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:36          client.py->line(103)|
=============================================== Client 17 Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.09s] │ [0.02s]           │ [0.01s]                   │ [0.01s] │ [11.32s]          │ [0.01s] │ [3.00s] │ [14.43s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:36          client.py->line(103)|
=============================================== Client 4  Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.16s] │ [0.02s]           │ [0.01s]                   │ [0.01s] │ [11.31s]          │ [0.01s] │ [3.01s] │ [14.51s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:36          client.py->line(103)|
=============================================== Client 1  Griewank(10D) ================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.14s] │ [0.01s]           │ [0.01s]                   │ [0.01s] │ [11.21s]          │ [0.00s] │ [3.01s] │ [14.36s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:36          client.py->line(103)|
=============================================== Client 18 Schwefel(10D) ================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.11s] │ [0.01s]           │ [0.01s]                   │ [0.01s] │ [11.18s]          │ [0.00s] │ [2.97s] │ [14.28s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:36          client.py->line(103)|
============================================== Client 16 Weierstrass(10D) ==============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.12s] │ [0.01s]           │ [0.01s]                   │ [0.01s] │ [11.18s]          │ [0.00s] │ [2.92s] │ [14.23s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:36   iaffbo_client.py->line(232)|Client 6 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-05 09:49:36   iaffbo_client.py->line(232)|Client 4 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:36   iaffbo_client.py->line(267)|Client 6 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.009s
WARNING 2025-08-05 09:49:36   iaffbo_client.py->line(267)|Client 4 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.009s
INFO    2025-08-05 09:49:36          client.py->line(103)|
=============================================== Client 14 Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.17s] │ [0.02s]           │ [0.02s]                   │ [0.02s] │ [11.09s]          │ [0.00s] │ [3.01s] │ [14.29s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:36   iaffbo_client.py->line(232)|Client 17 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-05 09:49:36   iaffbo_client.py->line(232)|Client 14 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-05 09:49:36   iaffbo_client.py->line(232)|Client 1 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-05 09:49:36   iaffbo_client.py->line(232)|Client 16 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:36   iaffbo_client.py->line(267)|Client 14 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.010s
WARNING 2025-08-05 09:49:36   iaffbo_client.py->line(267)|Client 17 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.014s
WARNING 2025-08-05 09:49:36   iaffbo_client.py->line(267)|Client 1 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.009s
WARNING 2025-08-05 09:49:36   iaffbo_client.py->line(267)|Client 16 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.011s
INFO    2025-08-05 09:49:36   iaffbo_client.py->line(232)|Client 18 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:36   iaffbo_client.py->line(267)|Client 18 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.008s
INFO    2025-08-05 09:49:36          client.py->line(103)|
================================================ Client 3  Ackley(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.14s] │ [0.00s]           │ [0.01s]                   │ [0.01s] │ [10.78s]          │ [0.00s] │ [2.79s] │ [13.72s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:36   iaffbo_client.py->line(232)|Client 3 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:36   iaffbo_client.py->line(267)|Client 3 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.004s
INFO    2025-08-05 09:49:36          client.py->line(103)|
=============================================== Client 2  Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.15s] │ [0.01s]           │ [0.01s]                   │ [0.01s] │ [10.83s]          │ [0.00s] │ [2.80s] │ [13.79s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:36   iaffbo_client.py->line(232)|Client 2 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:36   iaffbo_client.py->line(267)|Client 2 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.008s
INFO    2025-08-05 09:49:37          client.py->line(103)|
============================================== Client 10 Rosenbrock(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.15s] │ [0.03s]           │ [0.01s]                   │ [0.01s] │ [8.80s]           │ [0.01s] │ [1.69s] │ [10.65s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:37   iaffbo_client.py->line(232)|Client 10 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:37   iaffbo_client.py->line(267)|Client 10 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.03s, avg_attempt=0.024s
INFO    2025-08-05 09:49:38   iaffbo_server.py->line(42)|Server received weights from Client 12, version=5
INFO    2025-08-05 09:49:39          client.py->line(103)|
============================================== Client 12 Weierstrass(10D) ==============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.11s] │ [0.02s]           │ [0.01s]                   │ [0.01s] │ [8.97s]           │ [0.01s] │ [1.26s] │ [10.35s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:39   iaffbo_client.py->line(232)|Client 12 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:39   iaffbo_client.py->line(267)|Client 12 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.005s
INFO    2025-08-05 09:49:39   iaffbo_server.py->line(42)|Server received weights from Client 15, version=5
INFO    2025-08-05 09:49:41          client.py->line(103)|
=============================================== Client 15 Griewank(10D) ================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.15s] │ [0.02s]           │ [0.01s]                   │ [0.01s] │ [8.62s]           │ [0.01s] │ [1.24s] │ [10.03s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:41   iaffbo_client.py->line(232)|Client 15 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:41   iaffbo_client.py->line(267)|Client 15 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.006s
INFO    2025-08-05 09:49:41   iaffbo_server.py->line(42)|Server received weights from Client 11, version=5
INFO    2025-08-05 09:49:42          client.py->line(103)|
================================================ Client 11 Ackley(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.12s] │ [0.01s]           │ [0.01s]                   │ [0.01s] │ [8.53s]           │ [0.01s] │ [1.41s] │ [10.08s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-05 09:49:43   iaffbo_client.py->line(232)|Client 11 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-05 09:49:43   iaffbo_client.py->line(267)|Client 11 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.02s, avg_attempt=0.013s
