
                               ____                __         
            ____     __  __   / __/  ____ ___     / /_   ____ 
           / __ \   / / / /  / /_   / __ `__ \   / __/  / __ \
          / /_/ /  / /_/ /  / __/  / / / / / /  / /_   / /_/ /
         / .___/   \__, /  /_/    /_/ /_/ /_/   \__/   \____/ 
        /_/       /____/                                      

INFO    2025-08-04 12:50:05           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:50:05           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:50:05           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:50:05           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:50:05           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:50:05           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:50:05           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:50:05           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:50:05           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:50:05           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:50:05           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:50:05           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:50:05           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:50:05           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:50:05           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:50:05           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:50:05           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:50:05           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:50:05        launcher.py->line(168)|
╭───────────┬──────────┬────────────────┬─────────────┬───────────┬───────┬───────────┬────────╮
│  running  │  repeat  │    progress    │  algorithm  │  problem  │  iid  │  clients  │  save  │
├───────────┼──────────┼────────────────┼─────────────┼───────────┼───────┼───────────┼────────┤
│    1/1    │   2/20   │ [2/20][10.00%] │   IAFFBO    │ arxiv2017 │   2   │    18     │  True  │
╰───────────┴──────────┴────────────────┴─────────────┴───────────┴───────┴───────────┴────────╯
INFO    2025-08-04 12:50:05        launcher.py->line(185)|Server started.
INFO    2025-08-04 12:50:07           tools.py->line(207)|
===================== IAFFBOServer ====================
╭───────────────────┬───────────┬───────────┬─────────╮
│ Parameter         │  Default  │  Updates  │  Using  │
├───────────────────┼───────────┼───────────┼─────────┤
│ n_samples         │    100    │    100    │   100   │
├───────────────────┼───────────┼───────────┼─────────┤
│ batch_aggregation │     -     │   True    │    -    │
├───────────────────┼───────────┼───────────┼─────────┤
│ device            │     -     │   cuda    │    -    │
├───────────────────┼───────────┼───────────┼─────────┤
│ n_clusters        │     6     │     6     │    6    │
├───────────────────┼───────────┼───────────┼─────────┤
│ agg_interval      │    0.3    │    0.3    │   0.3   │
╰───────────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:50:07          client.py->line(111)|Client 1  started
INFO    2025-08-04 12:50:07          client.py->line(111)|Client 2  started
INFO    2025-08-04 12:50:07          client.py->line(111)|Client 3  started
INFO    2025-08-04 12:50:07          client.py->line(111)|Client 4  started
INFO    2025-08-04 12:50:07          server.py->line(162)|Client 1 join, total 1 clients
INFO    2025-08-04 12:50:07          client.py->line(111)|Client 5  started
INFO    2025-08-04 12:50:07          server.py->line(162)|Client 3 join, total 2 clients
INFO    2025-08-04 12:50:07          server.py->line(162)|Client 2 join, total 3 clients
INFO    2025-08-04 12:50:07          client.py->line(111)|Client 6  started
INFO    2025-08-04 12:50:07          server.py->line(162)|Client 4 join, total 4 clients
INFO    2025-08-04 12:50:07          client.py->line(111)|Client 7  started
INFO    2025-08-04 12:50:07          server.py->line(162)|Client 5 join, total 5 clients
INFO    2025-08-04 12:50:07          client.py->line(111)|Client 8  started
INFO    2025-08-04 12:50:07          client.py->line(89)|
==================== Client 1  Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Griewank   │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-04 12:50:07          server.py->line(162)|Client 6 join, total 6 clients
INFO    2025-08-04 12:50:07          client.py->line(111)|Client 9  started
INFO    2025-08-04 12:50:07          server.py->line(162)|Client 7 join, total 7 clients
INFO    2025-08-04 12:50:07          server.py->line(162)|Client 8 join, total 8 clients
INFO    2025-08-04 12:50:07          client.py->line(111)|Client 10 started
INFO    2025-08-04 12:50:07          client.py->line(89)|
==================== Client 4  Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Rastrigin  │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-04 12:50:07          client.py->line(111)|Client 11 started
INFO    2025-08-04 12:50:07          server.py->line(162)|Client 9 join, total 9 clients
INFO    2025-08-04 12:50:07          server.py->line(162)|Client 10 join, total 10 clients
INFO    2025-08-04 12:50:07          client.py->line(111)|Client 12 started
INFO    2025-08-04 12:50:07          client.py->line(111)|Client 13 started
INFO    2025-08-04 12:50:07          client.py->line(89)|
==================== Client 3  Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Ackley     │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-04 12:50:07          server.py->line(162)|Client 13 join, total 11 clients
INFO    2025-08-04 12:50:07          client.py->line(89)|
==================== Client 2  Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Rastrigin  │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-04 12:50:07          server.py->line(162)|Client 11 join, total 12 clients
INFO    2025-08-04 12:50:07          server.py->line(162)|Client 12 join, total 13 clients
INFO    2025-08-04 12:50:07          client.py->line(89)|
==================== Client 6  Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Schwefel   │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-04 12:50:07          client.py->line(111)|Client 14 started
INFO    2025-08-04 12:50:07          client.py->line(89)|
==================== Client 5  Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Ackley     │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-04 12:50:07          client.py->line(111)|Client 15 started
INFO    2025-08-04 12:50:07          client.py->line(111)|Client 16 started
INFO    2025-08-04 12:50:07          server.py->line(162)|Client 14 join, total 14 clients
INFO    2025-08-04 12:50:07          client.py->line(89)|
==================== Client 8  Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Sphere     │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-04 12:50:07          server.py->line(162)|Client 15 join, total 15 clients
INFO    2025-08-04 12:50:07          client.py->line(111)|Client 17 started
INFO    2025-08-04 12:50:07          client.py->line(89)|
==================== Client 7  Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Rastrigin  │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-04 12:50:07          server.py->line(162)|Client 16 join, total 16 clients
INFO    2025-08-04 12:50:07          client.py->line(111)|Client 18 started
INFO    2025-08-04 12:50:07          client.py->line(89)|
==================== Client 9  Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Ackley     │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-04 12:50:07          server.py->line(162)|Client 17 join, total 17 clients
INFO    2025-08-04 12:50:07          client.py->line(89)|
==================== Client 10 Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Rosenbrock │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-04 12:50:07          client.py->line(89)|
==================== Client 13 Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Rosenbrock │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-04 12:50:07          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-04 12:50:07          client.py->line(89)|
==================== Client 11 Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Ackley     │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-04 12:50:07          client.py->line(89)|
===================== Client 12 Params ====================
╭─────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName    │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├─────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Weierstrass │    10 │     1 │     2 │      50 │     110 │
╰─────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-04 12:50:07          client.py->line(89)|
==================== Client 14 Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Rastrigin  │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-04 12:50:07          client.py->line(89)|
==================== Client 15 Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Griewank   │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-04 12:50:07          client.py->line(89)|
===================== Client 16 Params ====================
╭─────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName    │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├─────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Weierstrass │    10 │     1 │     2 │      50 │     110 │
╰─────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-04 12:50:07          client.py->line(89)|
==================== Client 17 Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Rastrigin  │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-04 12:50:07          client.py->line(89)|
==================== Client 18 Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Schwefel   │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-04 12:50:07   iaffbo_client.py->line(232)|Client 3 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:50:07   iaffbo_client.py->line(267)|Client 3 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.002s
INFO    2025-08-04 12:50:07   iaffbo_client.py->line(232)|Client 9 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:50:07   iaffbo_client.py->line(267)|Client 9 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.003s
INFO    2025-08-04 12:50:07   iaffbo_client.py->line(232)|Client 8 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:50:07   iaffbo_client.py->line(267)|Client 8 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.002s
INFO    2025-08-04 12:50:07   iaffbo_client.py->line(232)|Client 13 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-04 12:50:07   iaffbo_client.py->line(232)|Client 14 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-04 12:50:07   iaffbo_client.py->line(232)|Client 16 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:50:07   iaffbo_client.py->line(267)|Client 13 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.02s, avg_attempt=0.017s
WARNING 2025-08-04 12:50:07   iaffbo_client.py->line(267)|Client 14 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.009s
WARNING 2025-08-04 12:50:07   iaffbo_client.py->line(267)|Client 16 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.008s
INFO    2025-08-04 12:50:08   iaffbo_client.py->line(232)|Client 2 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:50:08   iaffbo_client.py->line(267)|Client 2 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.005s
INFO    2025-08-04 12:50:08   iaffbo_client.py->line(232)|Client 12 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-04 12:50:08   iaffbo_client.py->line(232)|Client 4 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:50:08   iaffbo_client.py->line(267)|Client 4 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.003s
WARNING 2025-08-04 12:50:08   iaffbo_client.py->line(267)|Client 12 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.02s, avg_attempt=0.016s
INFO    2025-08-04 12:50:08   iaffbo_client.py->line(232)|Client 5 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-04 12:50:08   iaffbo_client.py->line(232)|Client 15 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-04 12:50:08   iaffbo_client.py->line(232)|Client 6 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-04 12:50:08   iaffbo_client.py->line(232)|Client 7 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-04 12:50:08   iaffbo_client.py->line(232)|Client 18 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:50:08   iaffbo_client.py->line(267)|Client 5 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.02s, avg_attempt=0.018s
WARNING 2025-08-04 12:50:08   iaffbo_client.py->line(267)|Client 6 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.007s
WARNING 2025-08-04 12:50:08   iaffbo_client.py->line(267)|Client 15 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.02s, avg_attempt=0.016s
WARNING 2025-08-04 12:50:08   iaffbo_client.py->line(267)|Client 7 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.008s
WARNING 2025-08-04 12:50:08   iaffbo_client.py->line(267)|Client 18 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.008s
INFO    2025-08-04 12:50:08   iaffbo_client.py->line(232)|Client 17 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-04 12:50:08   iaffbo_client.py->line(232)|Client 10 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:50:08   iaffbo_client.py->line(267)|Client 17 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.005s
WARNING 2025-08-04 12:50:08   iaffbo_client.py->line(267)|Client 10 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.02s, avg_attempt=0.007s
INFO    2025-08-04 12:50:08   iaffbo_client.py->line(232)|Client 11 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-04 12:50:08   iaffbo_client.py->line(232)|Client 1 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:50:08   iaffbo_client.py->line(267)|Client 11 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.02s, avg_attempt=0.019s
WARNING 2025-08-04 12:50:08   iaffbo_client.py->line(267)|Client 1 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.013s
INFO    2025-08-04 12:50:16          server.py->line(166)|Client 14 quit, remain 17 clients
INFO    2025-08-04 12:50:16          server.py->line(166)|Client 7 quit, remain 16 clients
INFO    2025-08-04 12:50:16          server.py->line(166)|Client 9 quit, remain 15 clients
