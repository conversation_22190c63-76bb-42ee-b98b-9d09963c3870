#!/usr/bin/env python3
"""
检查 msgpack 文件的内容结构
"""
import msgpack
import os

def check_msgpack_file(filepath):
    """检查单个 msgpack 文件的内容"""
    print(f"\n检查文件: {filepath}")
    try:
        # 尝试不同的解包选项
        with open(filepath, 'rb') as f:
            try:
                data = msgpack.unpack(f, raw=False, strict_map_key=False)
            except:
                f.seek(0)
                data = msgpack.unpack(f, raw=False)

        print(f"数据类型: {type(data)}")
        if isinstance(data, dict):
            print("字典键:")
            for key in data.keys():
                print(f"  - {key}: {type(data[key])}")
                if str(key).endswith('solutions') and hasattr(data[key], 'keys'):
                    print(f"    {key} 子键: {list(data[key].keys())}")
                elif key == 'solutions' and hasattr(data[key], 'keys'):
                    print(f"    solutions 子键: {list(data[key].keys())}")
        elif isinstance(data, list):
            print(f"列表长度: {len(data)}")
            if len(data) > 0:
                print(f"第一个元素类型: {type(data[0])}")

        return data
    except Exception as e:
        print(f"读取文件失败: {e}")
        return None

def main():
    # 检查 IAFFBO ARXIV2017 目录
    iaffbo_path = "out/results/IAFFBO/ARXIV2017/NIID2/Run 1.msgpack"
    if os.path.exists(iaffbo_path):
        iaffbo_data = check_msgpack_file(iaffbo_path)
    
    # 检查 FMDLES ARXIV2017 目录作为对比
    fmdles_path = "out/results/FMDLES/ARXIV2017/NIID2/Run 1.msgpack"
    if os.path.exists(fmdles_path):
        fmdles_data = check_msgpack_file(fmdles_path)
    
    # 检查 FMDLES IID 目录
    fmdles_iid_path = "out/results/FMDLES/ARXIV2017/IID/Run 1.msgpack"
    if os.path.exists(fmdles_iid_path):
        fmdles_iid_data = check_msgpack_file(fmdles_iid_path)

if __name__ == "__main__":
    main()
