#!/usr/bin/env python3
"""
快速检查IAFFBO客户端参数传输状态
"""

import re
import os
from collections import defaultdict


def analyze_log_file(log_file="out/logs/pyfmto.log"):
    """分析日志文件，统计客户端状态"""
    
    if not os.path.exists(log_file):
        print(f"日志文件不存在: {log_file}")
        return
    
    # 统计数据
    client_stats = defaultdict(lambda: {
        'push_attempts': 0,
        'push_success': 0,
        'push_failed': 0,
        'pull_attempts': 0,
        'pull_success': 0,
        'pull_failed': 0,
        'server_received': 0,
        'server_sent': 0
    })
    
    # 正则表达式
    patterns = {
        'push_start': re.compile(r'Client (\d+) starting PUSH_WEIGHTS'),
        'push_success': re.compile(r'Client (\d+) PUSH_WEIGHTS SUCCESS'),
        'push_failed': re.compile(r'Client (\d+) PUSH_WEIGHTS FAILED'),
        'pull_start': re.compile(r'Client (\d+) starting PULL_WEIGHTS'),
        'pull_success': re.compile(r'Client (\d+) PULL_WEIGHTS SUCCESS'),
        'pull_failed': re.compile(r'Client (\d+) PULL_WEIGHTS FAILED'),
        'server_received': re.compile(r'Server received weights from Client (\d+)'),
        'server_sent': re.compile(r'Server sent aggregated weights to Client (\d+)')
    }
    
    print(f"正在分析日志文件: {log_file}")
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                for event_type, pattern in patterns.items():
                    match = pattern.search(line)
                    if match:
                        client_id = int(match.group(1))
                        
                        if event_type == 'push_start':
                            client_stats[client_id]['push_attempts'] += 1
                        elif event_type == 'push_success':
                            client_stats[client_id]['push_success'] += 1
                        elif event_type == 'push_failed':
                            client_stats[client_id]['push_failed'] += 1
                        elif event_type == 'pull_start':
                            client_stats[client_id]['pull_attempts'] += 1
                        elif event_type == 'pull_success':
                            client_stats[client_id]['pull_success'] += 1
                        elif event_type == 'pull_failed':
                            client_stats[client_id]['pull_failed'] += 1
                        elif event_type == 'server_received':
                            client_stats[client_id]['server_received'] += 1
                        elif event_type == 'server_sent':
                            client_stats[client_id]['server_sent'] += 1
    
    except Exception as e:
        print(f"读取日志文件出错: {e}")
        return
    
    # 显示结果
    print("\n" + "=" * 100)
    print("IAFFBO 客户端参数传输状态统计")
    print("=" * 100)
    
    if not client_stats:
        print("未找到客户端活动记录")
        return
    
    # 表头
    print(f"{'客户端':<8} {'上传尝试':<8} {'上传成功':<8} {'上传失败':<8} {'下载尝试':<8} "
          f"{'下载成功':<8} {'下载失败':<8} {'服务器收到':<10} {'服务器发送':<10}")
    print("-" * 100)
    
    # 按客户端ID排序显示
    total_stats = defaultdict(int)
    
    for client_id in sorted(client_stats.keys()):
        stats = client_stats[client_id]
        
        print(f"Client {client_id:<2} "
              f"{stats['push_attempts']:<8} {stats['push_success']:<8} {stats['push_failed']:<8} "
              f"{stats['pull_attempts']:<8} {stats['pull_success']:<8} {stats['pull_failed']:<8} "
              f"{stats['server_received']:<10} {stats['server_sent']:<10}")
        
        # 累计总数
        for key in stats:
            total_stats[key] += stats[key]
    
    # 显示总计
    print("-" * 100)
    print(f"总计     "
          f"{total_stats['push_attempts']:<8} {total_stats['push_success']:<8} {total_stats['push_failed']:<8} "
          f"{total_stats['pull_attempts']:<8} {total_stats['pull_success']:<8} {total_stats['pull_failed']:<8} "
          f"{total_stats['server_received']:<10} {total_stats['server_sent']:<10}")
    
    # 分析结果
    print("\n分析结果:")
    print(f"- 活跃客户端数量: {len(client_stats)}")
    
    # 上传成功率
    if total_stats['push_attempts'] > 0:
        push_success_rate = (total_stats['push_success'] / total_stats['push_attempts']) * 100
        print(f"- 上传成功率: {push_success_rate:.1f}% ({total_stats['push_success']}/{total_stats['push_attempts']})")
    else:
        print("- 上传成功率: 无上传尝试")
    
    # 下载成功率
    if total_stats['pull_attempts'] > 0:
        pull_success_rate = (total_stats['pull_success'] / total_stats['pull_attempts']) * 100
        print(f"- 下载成功率: {pull_success_rate:.1f}% ({total_stats['pull_success']}/{total_stats['pull_attempts']})")
    else:
        print("- 下载成功率: 无下载尝试")
    
    # 服务器处理情况
    print(f"- 服务器接收参数次数: {total_stats['server_received']}")
    print(f"- 服务器发送参数次数: {total_stats['server_sent']}")
    
    # 问题诊断
    print("\n问题诊断:")
    
    # 检查是否有客户端从未成功上传
    never_pushed = [cid for cid, stats in client_stats.items() if stats['push_success'] == 0]
    if never_pushed:
        print(f"- 从未成功上传参数的客户端: {never_pushed}")
    
    # 检查是否有客户端从未成功下载
    never_pulled = [cid for cid, stats in client_stats.items() if stats['pull_success'] == 0]
    if never_pulled:
        print(f"- 从未成功下载参数的客户端: {never_pulled}")
    
    # 检查上传下载不匹配
    mismatch_clients = []
    for cid, stats in client_stats.items():
        if stats['push_success'] > 0 and stats['pull_success'] == 0:
            mismatch_clients.append(f"Client {cid} (只上传未下载)")
        elif stats['push_success'] == 0 and stats['pull_success'] > 0:
            mismatch_clients.append(f"Client {cid} (只下载未上传)")
    
    if mismatch_clients:
        print(f"- 上传下载不匹配的客户端: {mismatch_clients}")
    
    # 检查服务器处理异常
    if total_stats['server_received'] != total_stats['push_success']:
        print(f"- 警告: 服务器接收次数({total_stats['server_received']}) != 客户端上传成功次数({total_stats['push_success']})")
    
    if total_stats['server_sent'] != total_stats['pull_success']:
        print(f"- 警告: 服务器发送次数({total_stats['server_sent']}) != 客户端下载成功次数({total_stats['pull_success']})")


def main():
    import sys
    
    log_file = "out/logs/pyfmto.log"
    if len(sys.argv) > 1:
        log_file = sys.argv[1]
    
    analyze_log_file(log_file)


if __name__ == "__main__":
    main()
