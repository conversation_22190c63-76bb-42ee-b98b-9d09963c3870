import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from scipy.stats import norm
from sklearn.cluster import KMeans
from typing import Tuple, List, Dict, Optional
from enum import Enum, auto
import logging

logger = logging.getLogger(__name__)


class Actions(Enum):
    PULL_INIT = auto()
    PUSH_WEIGHTS = auto()
    PULL_WEIGHTS = auto()


class AcquisitionFunction:
    """Acquisition function implementations (EI, UCB, LCB) - exactly aligned with MATLAB AF function"""
    def __init__(self):
        pass

    def eval(self, pop_obj: np.ndarray, mse: np.ndarray, a1_obj: np.ndarray, ucb_flag: int) -> np.ndarray:
        """
        Compute acquisition function values - exactly matching MATLAB AF function
        
        function objs = AF(PopObj,MSE,A1Obj,UCB_Flag)

        Parameters:
        -----------
        pop_obj : np.ndarray
            PopObj - Predicted mean values from GP
        mse : np.ndarray  
            MSE - Predicted variances from GP
        a1_obj : np.ndarray
            A1Obj - Historical objective values (normalized)
        ucb_flag : int
            UCB_Flag (0=EI, 1=UCB, 2=LCB)

        Returns:
        --------
        np.ndarray
            objs - Acquisition function values
        """
        if ucb_flag == 1:
            # UCB: objs = PopObj + 2*sqrt(MSE);
            objs = pop_obj + 2 * np.sqrt(mse)
        elif ucb_flag == 0:
            # EI: Expected Improvement
            objs = self._eval_ei_matlab(pop_obj, mse, a1_obj)
        elif ucb_flag == 2:
            # LCB: objs = PopObj - 2*sqrt(MSE);
            objs = pop_obj - 2 * np.sqrt(mse)
        else:
            raise ValueError(f"Invalid UCB_Flag: {ucb_flag}")
        
        return objs

    @staticmethod
    def _eval_ei_matlab(pop_obj: np.ndarray, mse: np.ndarray, a1_obj: np.ndarray) -> np.ndarray:
        """
        Compute Expected Improvement - exactly matching MATLAB implementation
        
        MATLAB code:
        YY = PopObj;
        s = MSE;
        fmin = min(A1Obj);
        set=fmin-YY;
        s=s.^0.5;
        if s==0
            if YY<fmin
                E=fmin-YY;
            else
                E=0;
            end
        else
            E=set.*normcdf(set./s)+s.*normpdf(set./s);
        end
        objs=-E;
        """
        YY = pop_obj.copy()
        s = mse.copy()
        fmin = np.min(a1_obj)
        set_val = fmin - YY
        s = s ** 0.5  # s=s.^0.5 in MATLAB
        
        E = np.zeros_like(YY)
        
        # Handle each element (MATLAB processes element-wise)
        for i in range(len(YY.flatten())):
            idx = np.unravel_index(i, YY.shape)
            if s[idx] == 0:
                if YY[idx] < fmin:
                    E[idx] = fmin - YY[idx]
                else:
                    E[idx] = 0
            else:
                z = set_val[idx] / s[idx]
                E[idx] = set_val[idx] * norm.cdf(z) + s[idx] * norm.pdf(z)
        
        objs = -E  # objs=-E in MATLAB
        return objs


def generate_pairwise_data_matlab(a1_dec: np.ndarray, a1_obj_norm: np.ndarray, 
                                 pop_dec: np.ndarray, af_values: np.ndarray,
                                 privacy_noise_ratio: float = 0.0) -> Tuple[np.ndarray, np.ndarray]:
    """
    Generate pairwise comparison data exactly matching MATLAB implementation
    
    MATLAB code structure:
    1. Historical data pairwise comparisons (A1Dec vs A1Obj_norm)
    2. Privacy noise addition
    3. Sampled data pairwise comparisons (PopDec vs af_values)  
    4. Combine and shuffle
    
    Parameters:
    -----------
    a1_dec : np.ndarray
        A1Dec - Historical decision variables
    a1_obj_norm : np.ndarray  
        A1Obj_norm - Historical objective values (normalized)
    pop_dec : np.ndarray
        PopDec - Sampled decision variables
    af_values : np.ndarray
        objs - Acquisition function values for samples
    privacy_noise_ratio : float
        p - Privacy noise ratio (default 0.0)
        
    Returns:
    --------
    Tuple[np.ndarray, np.ndarray]
        XXs, YYs - Pairwise input features and labels
    """
    
    # Stage 1: Historical data pairwise comparisons
    # MATLAB: Input = A1Dec; Output = A1Obj_norm; 
    Input = a1_dec
    Output = a1_obj_norm.ravel()
    
    XXs = []
    YYs = []
    id = 0
    
    # MATLAB nested loops for historical data - vectorized for speed
    n = len(Input)
    if n > 1:
        # Create all pairs using broadcasting
        i_indices, j_indices = np.triu_indices(n, k=1)

        # Vectorized concatenation
        XXs_batch = np.concatenate([Input[i_indices], Input[j_indices]], axis=1)
        XXs.extend(XXs_batch)

        # Vectorized comparison
        output_diff = Output[i_indices] - Output[j_indices]
        YYs_batch = np.where(output_diff > 0, 1, np.where(output_diff < 0, -1, 0))
        YYs.extend(YYs_batch)

        id += len(i_indices)
    
    # Convert to numpy arrays efficiently
    if XXs:
        XXs = np.vstack(XXs) if isinstance(XXs[0], np.ndarray) else np.array(XXs)
        YYs = np.array(YYs)
    else:
        XXs = np.empty((0, Input.shape[1] * 2))
        YYs = np.empty((0,))
    
    # MATLAB: ix = randperm(id-1,id-1); XXs = XXs(ix,:); YYs = YYs(ix,:);
    if len(YYs) > 0:
        ix = np.random.permutation(len(YYs))
        XXs = XXs[ix]
        YYs = YYs[ix]
    
    # Privacy noise addition - exactly matching MATLAB
    # MATLAB: aa = round(rand(size(YYs(1:round(p*(id-1)),:),1),1));
    # aa(find(aa==0),:) = -1;
    # YYs(1:round(p*(id-1)),:) = YYs(1:round(p*(id-1)),:) .* aa;
    if privacy_noise_ratio > 0 and len(YYs) > 0:
        n_noise = int(np.round(privacy_noise_ratio * (id - 1)))
        if n_noise > 0:
            aa = np.round(np.random.rand(n_noise))
            aa[aa == 0] = -1  # aa(find(aa==0),:) = -1;
            YYs[:n_noise] = YYs[:n_noise] * aa
    
    # MATLAB: ix = randperm(id-1,id-1); XXs = XXs(ix,:); YYs = YYs(ix,:);
    if len(YYs) > 0:
        ix = np.random.permutation(len(YYs))
        XXs = XXs[ix]
        YYs = YYs[ix]
    
    # Stage 2: Sampled data pairwise comparisons  
    # MATLAB: Input = PopDec; Output = objs(1:size(PopDec,1),1);
    Input_samples = pop_dec
    Output_samples = af_values[:len(pop_dec)].ravel()
    
    XXs_ = []
    YYs_ = []
    id = 0
    
    # MATLAB nested loops for sampled data - vectorized for speed
    n_samples = len(Input_samples)
    if n_samples > 1:
        # Create all pairs using broadcasting
        i_indices, j_indices = np.triu_indices(n_samples, k=1)

        # Vectorized concatenation
        XXs_batch = np.concatenate([Input_samples[i_indices], Input_samples[j_indices]], axis=1)
        XXs_.extend(XXs_batch)

        # Vectorized comparison
        output_diff = Output_samples[i_indices] - Output_samples[j_indices]
        YYs_batch = np.where(output_diff > 0, 1, np.where(output_diff < 0, -1, 0))
        YYs_.extend(YYs_batch)

        id += len(i_indices)
    
    # Convert to numpy arrays efficiently
    if XXs_:
        XXs_ = np.vstack(XXs_) if isinstance(XXs_[0], np.ndarray) else np.array(XXs_)
        YYs_ = np.array(YYs_)
    else:
        XXs_ = np.empty((0, Input_samples.shape[1] * 2))
        YYs_ = np.empty((0,))
    
    # MATLAB: ix = randperm(id-1,id-1); XXs_ = XXs_(ix,:); YYs_ = YYs_(ix,:);
    if len(YYs_) > 0:
        ix = np.random.permutation(len(YYs_))
        XXs_ = XXs_[ix]
        YYs_ = YYs_[ix]
    
    # Stage 3: Combine data
    # MATLAB: XXs = [XXs;XXs_]; YYs = [YYs;YYs_];
    if len(XXs) > 0 and len(XXs_) > 0:
        XXs_combined = np.vstack([XXs, XXs_])
        YYs_combined = np.concatenate([YYs, YYs_])
    elif len(XXs) > 0:
        XXs_combined = XXs
        YYs_combined = YYs
    elif len(XXs_) > 0:
        XXs_combined = XXs_
        YYs_combined = YYs_
    else:
        XXs_combined = np.empty((0, a1_dec.shape[1] * 2))
        YYs_combined = np.empty((0,))
    
    # Final shuffle
    # MATLAB: ix = randperm(size(XXs,1),size(XXs,1)); XXs = XXs(ix,:); YYs = YYs(ix,:);
    if len(YYs_combined) > 0:
        ix = np.random.permutation(len(YYs_combined))
        XXs_combined = XXs_combined[ix]
        YYs_combined = YYs_combined[ix]
    
    return XXs_combined, YYs_combined


def data_process(input_data: np.ndarray, output_data: np.ndarray, train_ratio: float = 3/4) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
    """
    Data processing function exactly matching MATLAB DataProcess.m

    MATLAB code:
    pha = 3/4;
    index0 = find(Output==0);
    indexp1 = find(Output == 1);
    indexn1 = find(Output == -1);
    
    K0 = false(1,length(index0));
    Kp1 = false(1,length(indexp1));
    Kn1 = false(1,length(indexn1));
    
    K0(randperm(length(index0),ceil(pha*length(index0)))) = true;
    Kp1(randperm(length(indexp1),ceil(pha*length(indexp1)))) = true;
    Kn1(randperm(length(indexn1),ceil(pha*length(indexn1)))) = true;

    Parameters:
    -----------
    input_data : np.ndarray
        Input features (XXs)
    output_data : np.ndarray
        Output labels (YYs)
    train_ratio : float
        Ratio of training data (default 3/4 = 0.75, matching MATLAB pha = 3/4)

    Returns:
    --------
    Tuple containing (TrainIn, TrainOut, TestIn, TestOut)
    """
    if len(output_data) == 0:
        return np.empty((0, input_data.shape[1])), np.empty((0,)), np.empty((0, input_data.shape[1])), np.empty((0,))

    # MATLAB: index0 = find(Output==0); indexp1 = find(Output == 1); indexn1 = find(Output == -1);
    index0 = np.where(output_data == 0)[0]
    indexp1 = np.where(output_data == 1)[0]
    indexn1 = np.where(output_data == -1)[0]

    # MATLAB: K0 = false(1,length(index0)); etc.
    train_indices = []

    # MATLAB: K0(randperm(length(index0),ceil(pha*length(index0)))) = true;
    if len(index0) > 0:
        n_train_0 = int(np.ceil(train_ratio * len(index0)))
        train_0_mask = np.random.choice(len(index0), n_train_0, replace=False)
        train_indices.extend(index0[train_0_mask])

    # MATLAB: Kp1(randperm(length(indexp1),ceil(pha*length(indexp1)))) = true;
    if len(indexp1) > 0:
        n_train_p1 = int(np.ceil(train_ratio * len(indexp1)))
        train_p1_mask = np.random.choice(len(indexp1), n_train_p1, replace=False)
        train_indices.extend(indexp1[train_p1_mask])

    # MATLAB: Kn1(randperm(length(indexn1),ceil(pha*length(indexn1)))) = true;
    if len(indexn1) > 0:
        n_train_n1 = int(np.ceil(train_ratio * len(indexn1)))
        train_n1_mask = np.random.choice(len(indexn1), n_train_n1, replace=False)
        train_indices.extend(indexn1[train_n1_mask])

    train_indices = np.array(train_indices)
    
    # MATLAB: TestIn = Input(setdiff(1:size(Input,1),K),:);
    test_indices = np.setdiff1d(np.arange(len(output_data)), train_indices)

    # MATLAB: TrainIn = Input(K,:); TrainOut = Output(K);
    train_in = input_data[train_indices]
    train_out = output_data[train_indices]
    test_in = input_data[test_indices]
    test_out = output_data[test_indices]

    # MATLAB: Train_randindex = randperm(size(TrainOut,1),size(TrainOut,1));
    if len(train_out) > 0:
        train_shuffle = np.random.permutation(len(train_out))
        train_in = train_in[train_shuffle]
        train_out = train_out[train_shuffle]

    # MATLAB: Test_randindex = randperm(size(TestOut,1),size(TestOut,1));
    if len(test_out) > 0:
        test_shuffle = np.random.permutation(len(test_out))
        test_in = test_in[test_shuffle]
        test_out = test_out[test_shuffle]

    return train_in, train_out, test_in, test_out


def onehot_conv(labels: np.ndarray, mode: int) -> np.ndarray:
    """
    One-hot conversion function exactly matching MATLAB onehotconv.m
    
    MATLAB code:
    if varargin{2}== 1
        l_onehot = zeros(size(l,1),2);
        l_onehot(l == 1 ,1) = 1;
        l_onehot(l == -1,3) = 1;  % Note: accesses column 3, matrix auto-expands to 3 columns
        varargout = {l_onehot};
    elseif varargin{2} == 2
        [~,maxind] = max(onehot_l,[],2);
        res_l(maxind==1) = 1;
        res_l(maxind==2) = -1;
        varargout = {res_l};
    """
    if mode == 1:
        # Convert to one-hot - exactly matching MATLAB behavior
        # MATLAB creates 2-column matrix but accesses column 3, causing auto-expansion to 3 columns
        l_onehot = np.zeros((len(labels), 3))  # Create 3 columns to match MATLAB auto-expansion
        l_onehot[labels == 1, 0] = 1   # l_onehot(l == 1 ,1) = 1; (MATLAB 1-based -> Python 0-based)
        l_onehot[labels == -1, 2] = 1  # l_onehot(l == -1,3) = 1; (MATLAB 1-based -> Python 0-based)
        # Note: Column 1 (Python index 1) remains all zeros, will be deleted later
        return l_onehot
    elif mode == 2:
        # Convert from one-hot back to labels - exactly matching MATLAB
        # After column deletion, we have 2 columns: [label_1, label_-1]
        res_l = np.zeros(len(labels))
        max_ind = np.argmax(labels, axis=1)  # [~,maxind] = max(onehot_l,[],2);
        res_l[max_ind == 0] = 1   # Column 0 -> label 1
        res_l[max_ind == 1] = -1  # Column 1 -> label -1 (originally column 3 before deletion)
        return res_l.astype(int)
    else:
        raise ValueError("Mode must be 1 or 2")


class NeuralNetworkClassifier:
    """Neural network classifier for pairwise comparisons - aligned with MATLAB patternnet"""

    def __init__(self, input_dim: int, hidden_dims: List[int]):
        """
        Initialize neural network classifier

        Parameters:
        -----------
        input_dim : int
            Input dimension (2 * problem_dim)
        hidden_dims : List[int]
            Hidden layer dimensions
        """
        self.input_dim = input_dim
        self.hidden_dims = hidden_dims
        self.device = torch.device('cpu')  # Use CPU only for better performance in this scenario

        layers = []
        prev_dim = input_dim

        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU()
            ])
            prev_dim = hidden_dim

        # Output layer (2 classes: for labels 1 and -1, skip 0)
        # No activation function here since we use BCEWithLogitsLoss
        layers.append(nn.Linear(prev_dim, 2))
        self.network = nn.Sequential(*layers)
        self.optimizer = optim.Adam(self.network.parameters(), lr=0.001)
        # Use BCEWithLogitsLoss to match MATLAB's crossentropy more closely
        self.criterion = nn.BCEWithLogitsLoss()

    def train(self, x: np.ndarray, y: np.ndarray, bd: np.ndarray, bu: np.ndarray, epochs: int = 100):
        """
        Train the classifier exactly matching MATLAB training process

        MATLAB code:
        [TrainIn,TrainOut,TestIn,TestOut] = DataProcess(XXs,YYs);
        TrainIn_nor = (TrainIn - bd) / (bu - bd);
        TrainOut_onehot = onehotconv(TrainOut,1);
        TrainOut_onehot(:,2) =[];  % KEY: Delete column 2
        net = train(net,TrainIn_nor',TrainOut_onehot');

        MATLAB patternnet uses crossentropy loss: ce = -t .* log(y)
        """
        if len(y) == 0:
            return

        # MATLAB: [TrainIn,TrainOut,TestIn,TestOut] = DataProcess(XXs,YYs);
        train_in, train_out, test_in, test_out = data_process(x, y)

        if len(train_out) == 0:
            return

        # MATLAB: TrainIn_nor = (TrainIn - bd) / (bu - bd);
        # MATLAB uses broadcasting: TrainIn is N×(2*D), bd/bu are 1×D
        # MATLAB automatically broadcasts bd/bu to match TrainIn dimensions
        # We need to replicate this exact behavior

        # Create extended bounds by repeating bd and bu to match 2*D dimensions
        # This matches MATLAB's broadcasting behavior exactly
        bd_extended = np.tile(bd, 2)  # Repeat bd twice: [bd, bd]
        bu_extended = np.tile(bu, 2)  # Repeat bu twice: [bu, bu]
        train_in_norm = (train_in - bd_extended) / (bu_extended - bd_extended)

        # MATLAB: TrainOut_onehot = onehotconv(TrainOut,1);
        train_out_onehot = onehot_conv(train_out, 1)

        # MATLAB: TrainOut_onehot(:,2) =[]; % KEY: Delete column 2 (MATLAB 1-based indexing)
        # This is critical - MATLAB deletes the second column (index 1 in Python)!
        if train_out_onehot.shape[1] > 2:  # Should have 3 columns after onehot_conv
            train_out_onehot = np.delete(train_out_onehot, 1, axis=1)  # Delete column 2 (Python index 1)

        # Filter out samples with label 0 (they have all-zero one-hot encoding)
        valid_mask = (train_out != 0)
        if not np.any(valid_mask):
            return

        train_in_norm = train_in_norm[valid_mask]
        train_out_onehot = train_out_onehot[valid_mask]

        # Convert to tensors (CPU only)
        x_tensor = torch.FloatTensor(train_in_norm)

        # After deleting column 2, we should have 2 columns remaining
        # Column 0: for label 1, Column 1: for label -1 (originally column 3)
        if train_out_onehot.shape[1] == 2:
            # MATLAB-compatible loss calculation
            # MATLAB patternnet with crossentropy: ce = -t .* log(y)
            # We need to use BCELoss instead of CrossEntropyLoss to match MATLAB exactly
            y_tensor = torch.FloatTensor(train_out_onehot)

            # Use BCEWithLogitsLoss for numerical stability (combines sigmoid + BCE)
            # This matches MATLAB's crossentropy calculation more closely
            criterion = nn.BCEWithLogitsLoss()

            # Ensure network output layer matches
            if self.network[-1].out_features != 2:
                last_layer = self.network[-1]
                self.network[-1] = nn.Linear(last_layer.in_features, 2)
                self.optimizer = optim.Adam(self.network.parameters(), lr=0.001)
        else:
            # Fallback case - shouldn't happen with correct implementation
            y_tensor = torch.FloatTensor(train_out_onehot)
            criterion = nn.BCEWithLogitsLoss()

        self.network.train()
        for epoch in range(epochs):
            self.optimizer.zero_grad()
            outputs = self.network(x_tensor)

            # MATLAB-compatible loss calculation
            # BCEWithLogitsLoss matches MATLAB's crossentropy better than CrossEntropyLoss
            loss = criterion(outputs, y_tensor)

            loss.backward()

            # Gradient clipping with try-catch for compatibility
            try:
                torch.nn.utils.clip_grad_norm_(self.network.parameters(), max_norm=1.0)
            except RuntimeError:
                # Fallback for older PyTorch versions or parameter issues
                for param in self.network.parameters():
                    if param.grad is not None:
                        param.grad.data.clamp_(-1.0, 1.0)

            self.optimizer.step()

    def predict(self, x: np.ndarray, bd: np.ndarray, bu: np.ndarray) -> np.ndarray:
        """
        Predict pairwise comparisons exactly matching MATLAB prediction

        MATLAB code:
        XXs_tst_nor = (XXs_opt - bd) / (bu - bd);
        XXs_opt_Pre = onehotconv(net_opt(XXs_tst_nor')',2);

        MATLAB patternnet outputs probabilities via softmax, we need to match this
        """
        if len(x) == 0:
            return np.array([])

        # MATLAB: XXs_tst_nor = (XXs_opt - bd) / (bu - bd);
        # MATLAB uses broadcasting: XXs_opt is N×(2*D), bd/bu are 1×D
        # MATLAB automatically broadcasts bd/bu to match XXs_opt dimensions
        # We need to replicate this exact behavior

        # Create extended bounds by repeating bd and bu to match 2*D dimensions
        # This matches MATLAB's broadcasting behavior exactly
        bd_extended = np.tile(bd, 2)  # Repeat bd twice: [bd, bd]
        bu_extended = np.tile(bu, 2)  # Repeat bu twice: [bu, bu]
        x_norm = (x - bd_extended) / (bu_extended - bd_extended)

        self.network.eval()
        with torch.no_grad():
            # CPU-optimized prediction without GPU transfer overhead
            x_tensor = torch.FloatTensor(x_norm)
            outputs = self.network(x_tensor)

            # Apply sigmoid to match MATLAB patternnet output behavior
            # MATLAB patternnet uses softmax in output layer, but for binary classification
            # with BCEWithLogitsLoss, we need sigmoid to get probabilities
            outputs_prob = torch.sigmoid(outputs)

            # Network outputs 2 classes after column deletion
            # outputs[:, 0] corresponds to label 1, outputs[:, 1] corresponds to label -1
            # MATLAB: onehotconv(net_opt(XXs_tst_nor')',2);
            predictions = onehot_conv(outputs_prob.numpy(), 2)

            return predictions

    def get_weights(self) -> Dict:
        """Get network weights"""
        return {name: param.cpu().detach().numpy() for name, param in self.network.named_parameters()}

    def set_weights(self, weights: Dict):
        """Set network weights"""
        state_dict = {name: torch.FloatTensor(weight).to(self.device) for name, weight in weights.items()}
        self.network.load_state_dict(state_dict)


def aggregate_classifier_weights(client_weights: List[Dict], similarity_groups: List[List[int]]) -> Dict[int, Dict]:
    """
    Aggregate classifier weights based on task similarity
    
    Parameters:
    -----------
    client_weights : List[Dict]
        List of client classifier weights
    similarity_groups : List[List[int]]
        Groups of similar clients
        
    Returns:
    --------
    Dict[int, Dict]
        Aggregated weights for each client
    """
    aggregated_weights = {}
    
    for group in similarity_groups:
        if len(group) == 0:
            continue
            
        # Average weights within the group
        group_weights = {}
        first_client_weights = client_weights[group[0]]
        
        for param_name in first_client_weights.keys():
            # Initialize with zeros (ensure float type)
            avg_weight = np.zeros_like(first_client_weights[param_name], dtype=np.float64)

            # Sum weights from all clients in the group
            for client_id in group:
                if client_id < len(client_weights) and param_name in client_weights[client_id]:
                    avg_weight += client_weights[client_id][param_name].astype(np.float64)

            # Average
            avg_weight = avg_weight / len(group)
            group_weights[param_name] = avg_weight
            
        # Assign aggregated weights to all clients in the group
        for client_id in group:
            aggregated_weights[client_id] = group_weights.copy()
            
    return aggregated_weights


def convert_model_para_to_vector_matlab(netsum: List[Dict], cid: int) -> np.ndarray:
    """
    Convert model parameters to vector exactly matching MATLAB ConvertModelParaToVector
    
    MATLAB code:
    function weightvector = ConvertModelParaToVector(netsum,CID)
    net = netsum{CID};
    weightvector = [];
    
    % IW weights
    aa = netsum{CID}.IW{1,1};
    IWVector = reshape(aa,1,size(aa,1)*size(aa,2));
    weightvector = [weightvector IWVector];
    
    % LW weights  
    for i = size(net.LW,1)
        for j = size(net.LW,2)
            if ~isempty(net.LW{i,j})
                aa = netsum{CID}.LW{i,j};
                LWVector = reshape(aa,1,size(aa,1)*size(aa,2));
                weightvector = [weightvector LWVector];
            end
        end
    end
    
    % Bias weights
    for i = size(net.b,1)
        if ~isempty(net.b{i,1})
            aa = netsum{CID}.b{i,1};
            BVector = reshape(aa,1,size(aa,1)*size(aa,2));
            weightvector = [weightvector BVector];
        end
    end
    """
    net = netsum[cid]
    weightvector = []
    
    # Convert PyTorch state_dict to MATLAB-like structure
    # IW weights (Input-to-Hidden weights) - first layer weights
    first_layer_key = None
    for key in net.keys():
        if 'weight' in key and '0.' in key:  # First layer weights
            first_layer_key = key
            break
    
    if first_layer_key:
        aa = net[first_layer_key]
        IWVector = aa.flatten()  # reshape to 1D like MATLAB
        weightvector.extend(IWVector)
    
    # LW weights (Layer weights) - hidden layer weights
    layer_keys = sorted([k for k in net.keys() if 'weight' in k and k != first_layer_key])
    for key in layer_keys:
        aa = net[key]
        LWVector = aa.flatten()
        weightvector.extend(LWVector)
    
    # Bias weights
    bias_keys = sorted([k for k in net.keys() if 'bias' in k])
    for key in bias_keys:
        aa = net[key]
        BVector = aa.flatten()
        weightvector.extend(BVector)
    
    return np.array(weightvector)


def compute_task_similarity_matlab(client_weights: List[Dict], n_clusters: int = 6) -> np.ndarray:
    """
    Compute task similarity exactly matching MATLAB implementation
    
    MATLAB code:
    weightvector = [];
    for ff = 1:client_num
        weightvector(ff,:) = ConvertModelParaToVector(netsum,ff);
    end
    [associate,~]=kmeans(weightvector,cl_num);
    """
    client_num = len(client_weights)
    
    if client_num <= 1:
        return np.array([1] * client_num)  # All clients in same group
    
    # MATLAB: weightvector(ff,:) = ConvertModelParaToVector(netsum,ff);
    weightvector = []
    for ff in range(client_num):
        weight_vec = convert_model_para_to_vector_matlab(client_weights, ff)
        weightvector.append(weight_vec)
    
    weightvector = np.array(weightvector)
    
    # MATLAB: [associate,~]=kmeans(weightvector,cl_num);
    n_clusters = min(n_clusters, client_num)
    kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
    associate = kmeans.fit_predict(weightvector)
    
    # MATLAB uses 1-based indexing, so we add 1
    associate = associate + 1
    
    return associate


def model_agg_matlab(netsum: List[Dict], cid_list: List[int]) -> Dict:
    """
    Model aggregation exactly matching MATLAB model_agg function
    
    MATLAB code:
    function net_agg = model_agg(netsum,CID)
    net_ = netsum{1};
    client_num = size(CID,2);
    
    % IW aggregation
    IW_agg = zeros(size(netsum{1}.IW{1,1},1),size(netsum{1}.IW{1,1},2));
    for i = CID
        IW_agg = IW_agg + netsum{i}.IW{1,1};
    end
    IW_agg = IW_agg/client_num;
    net_.IW{1,1} = IW_agg;
    
    % Similar for LW and b...
    """
    if not cid_list:
        return netsum[0].copy()
    
    # Convert to 0-based indexing
    cid_list = [cid - 1 for cid in cid_list if cid > 0]
    client_num = len(cid_list)
    
    if client_num == 0:
        return netsum[0].copy()
    
    # Start with first client's network structure
    net_agg = {}
    
    # Get all parameter names from first client
    first_client = netsum[cid_list[0]]
    
    # Aggregate each parameter
    for param_name in first_client.keys():
        # Initialize aggregation sum
        param_sum = np.zeros_like(first_client[param_name])
        
        # Sum parameters from all clients in the group
        for cid in cid_list:
            if param_name in netsum[cid]:
                param_sum += netsum[cid][param_name]
        
        # Average
        net_agg[param_name] = param_sum / client_num
    
    return net_agg


class CompetitiveSwarmOptimizer:
    """Competitive Swarm Optimizer for optimizing implicit acquisition function - aligned with MATLAB CSO"""

    def __init__(self, dim: int, bounds: Tuple[np.ndarray, np.ndarray],
                 phi: float = 0.1, max_iterations: int = 100, pop_size: int = 100):
        """
        Initialize Competitive Swarm Optimizer

        Parameters:
        -----------
        dim : int
            Problem dimension
        bounds : Tuple[np.ndarray, np.ndarray]
            Lower and upper bounds
        phi : float
            Learning parameter
        max_iterations : int
            Maximum iterations (wmax in MATLAB)
        pop_size : int
            Population size (must be even)
        """
        self.dim = dim
        self.lb, self.ub = bounds
        self.phi = phi
        self.max_iterations = max_iterations
        self.pop_size = pop_size if pop_size % 2 == 0 else pop_size + 1

    def optimize(self, classifier: NeuralNetworkClassifier, bd: np.ndarray, bu: np.ndarray,
                 existing_solutions: Optional[np.ndarray] = None) -> np.ndarray:
        """
        Optimize using competitive swarm optimizer exactly matching MATLAB implementation
        
        MATLAB code structure:
        1. Initialize population (Decs) with existing solutions
        2. Ensure even population size
        3. CSO iterations with pairwise comparisons
        4. Return random solution from final population
        
        Key MATLAB code:
        if ~isempty(Decs_temp)
            Decs = rand(popsize-size(Decs_temp,1),D)*(bu-bd)+bd;
            Decs = [Decs;Decs_temp];
            if mod(popsize,2)==1
                Decs = [rand(1,D)*(bu-bd)+bd;Decs];
        else
            Decs = rand(popsize,D)*(bu-bd)+bd;
        end
        """
        # MATLAB population initialization logic
        if existing_solutions is not None and len(existing_solutions) > 0:
            # MATLAB: Decs = rand(popsize-size(Decs_temp,1),D)*(bu-bd)+bd;
            n_existing = len(existing_solutions)
            n_random = max(0, self.pop_size - n_existing)
            
            if n_random > 0:
                random_pop = np.random.rand(n_random, self.dim) * (bu - bd) + bd
                # MATLAB: Decs = [Decs;Decs_temp];
                population = np.vstack([random_pop, existing_solutions])
            else:
                population = existing_solutions[:self.pop_size].copy()
            
            popsize = len(population)
            
            # MATLAB: if mod(popsize,2)==1; Decs = [rand(1,D)*(bu-bd)+bd;Decs];
            if popsize % 2 == 1:
                extra_individual = np.random.rand(1, self.dim) * (bu - bd) + bd
                population = np.vstack([extra_individual, population])
                popsize = len(population)
        else:
            # MATLAB: Decs = rand(popsize,D)*(bu-bd)+bd;
            population = np.random.rand(self.pop_size, self.dim) * (bu - bd) + bd
            popsize = self.pop_size

        # MATLAB: shuffleid = randperm(size(Decs,1),popsize); Decs = Decs(shuffleid,:);
        shuffle_indices = np.random.permutation(popsize)
        population = population[shuffle_indices]

        # Initialize velocities (MATLAB initializes inside loop, but we do it here)
        loser_vel = None
        winner_vel = None

        # MATLAB: for j = 1:wmax
        for j in range(self.max_iterations):
            # MATLAB: rank = randperm(popsize);
            rank = np.random.permutation(popsize)
            # MATLAB: loser = rank(1:end/2); winner = rank(end/2+1:end);
            loser = rank[:popsize//2]
            winner = rank[popsize//2:]

            # MATLAB: XXs_opt construction - vectorized for speed
            # Vectorized concatenation of loser and winner pairs
            xxs_opt = np.concatenate([population[loser], population[winner]], axis=1)

            # MATLAB: XXs_tst_nor = (XXs_opt - bd) / (bu - bd);
            # MATLAB uses broadcasting: XXs_opt is N×(2*D), bd/bu are 1×D
            # MATLAB automatically broadcasts bd/bu to match XXs_opt dimensions
            # We need to replicate this exact behavior

            # Create extended bounds by repeating bd and bu to match 2*D dimensions
            # This matches MATLAB's broadcasting behavior exactly
            bd_extended = np.tile(bd, 2)  # Repeat bd twice: [bd, bd]
            bu_extended = np.tile(bu, 2)  # Repeat bu twice: [bu, bu]
            xxs_tst_nor = (xxs_opt - bd_extended) / (bu_extended - bd_extended)

            # MATLAB: XXs_opt_Pre = onehotconv(net_opt(XXs_tst_nor')',2);
            predictions = classifier.predict(xxs_tst_nor, bd, bu)

            # MATLAB: replace = find(XXs_opt_Pre==-1);
            replace = np.where(predictions == -1)[0]

            # MATLAB: temp = loser(replace); loser(replace) = winner(replace); winner(replace) = temp;
            if len(replace) > 0:
                temp = loser[replace].copy()
                loser[replace] = winner[replace]
                winner[replace] = temp

            # MATLAB: LoserDec = Decs(loser,:); WinnerDec = Decs(winner,:);
            loser_dec = population[loser]
            winner_dec = population[winner]

            # MATLAB: if j==1; LoserVel = zeros(size(LoserDec)); WinnerVel = zeros(size(WinnerDec)); end
            if j == 0:  # First iteration (j==1 in MATLAB, j==0 in Python)
                loser_vel = np.zeros_like(loser_dec)
                winner_vel = np.zeros_like(winner_dec)

            # MATLAB: R1 = repmat(rand(popsize/2,1),1,D);
            R1 = np.tile(np.random.rand(popsize//2, 1), (1, self.dim))
            R2 = np.tile(np.random.rand(popsize//2, 1), (1, self.dim))
            R3 = np.tile(np.random.rand(popsize//2, 1), (1, self.dim))

            # MATLAB: LoserVel = R1.*LoserVel + phi*R2.*(WinnerDec-LoserDec) + R3*(1-0).*(repmat(mean([Decs],1),popsize/2,1)-LoserDec);
            mean_position = np.mean(population, axis=0)
            mean_position_repeated = np.tile(mean_position, (popsize//2, 1))

            loser_vel = (R1 * loser_vel + 
                        self.phi * R2 * (winner_dec - loser_dec) + 
                        R3 * (1 - 0) * (mean_position_repeated - loser_dec))

            # MATLAB: LoserDec = LoserDec + LoserVel;
            loser_dec = loser_dec + loser_vel

            # MATLAB: LoserDec(LoserDec< bd*ones(size(LoserDec,1),size(LoserDec,2)))=bd;
            # LoserDec(LoserDec> bu*ones(size(LoserDec,1),size(LoserDec,2)))=bu;
            loser_dec = np.clip(loser_dec, bd, bu)

            # MATLAB: Decs = [WinnerDec;LoserDec];
            population = np.vstack([winner_dec, loser_dec])

        # MATLAB: PopNew = Decs(randperm(size(Decs,1),1),:);
        random_idx = np.random.randint(len(population))
        return population[random_idx]
