from pyDOE import lhs
from pyfmto.framework import Server, ClientPackage, ServerPackage
from pyfmto.utilities import logger
from .iaffbo_utils import (
    Actions,
    compute_task_similarity_matlab,
    model_agg_matlab
)
from .addfbo_utils import ClientsData


class IAFFBOServer(Server):
    """
    n_clusters: 6
    n_samples: 100
    agg_interval: 0.3
    """
    def __init__(self, **kwargs):
        super().__init__()
        kwargs = self.update_kwargs(kwargs)
        self.n_clusters = kwargs['n_clusters']
        self.n_samples = kwargs['n_samples']
        self.x_hat = None
        self.set_agg_interval(kwargs.get('agg_interval'))
        self.clients_data = ClientsData()

    def handle_request(self, client_data: ClientPackage) -> ServerPackage:
        if client_data.action == Actions.PUSH_WEIGHTS:
            return self._handle_push_weights(client_data)
        elif client_data.action == Actions.PULL_WEIGHTS:
            return self._handle_pull_weights(client_data)
        elif client_data.action == Actions.PULL_INIT:
            return self._handle_pull_init(client_data)
        else:
            logger.warning(f"Unknown action: {client_data.action}")
            return ServerPackage('error', {'status': 'unknown_action'})

    def _handle_push_weights(self, client_data: ClientPackage) -> ServerPackage:
        client_id = client_data.cid
        data = client_data.data
        self.clients_data.update_src(cid=client_id, version=data['version'], data=data['weights'])
        return ServerPackage('success', {'status': 'weights_received'})

    def _handle_pull_weights(self, client_data: ClientPackage) -> ServerPackage:
        client_id = client_data.cid
        version = client_data.data['version']
        clt_res = self.clients_data.get_res(client_id)
        if version != clt_res.version:
            return ServerPackage('data', None)
        else:
            return ServerPackage('success', {'weights': clt_res.data, 'x_hat': self.x_hat})

    def _handle_pull_init(self, client_data: ClientPackage) -> ServerPackage:
        self.dim = client_data.data['dim']
        if self.x_hat is None:
            self.gen_x_hat()
        pkg = ServerPackage('success', self.x_hat)
        return pkg

    def lts_res_ver(self, cid: int):
        return self.clients_data.get_res(cid).version

    @property
    def lts_sync_src_ver(self):
        min_ver, len_ver = self.clients_data.available_src_ver
        res = min_ver if len_ver == self.num_clients else -1
        return res

    def aggregate(self, client_id: int):
        """
        Aggregate client weights exactly matching MATLAB implementation

        MATLAB code:
        % Clustering the model weights
        weightvector = [];
        for ff = 1:client_num
            weightvector(ff,:) = ConvertModelParaToVector(netsum,ff);
        end
        [associate,~]=kmeans(weightvector,cl_num);

        % Then in CSO phase:
        CID = find(associate==associate(ff));
        net_agg = model_agg(netsum,CID');
        """
        src_ver = self.lts_sync_src_ver
        res_ver = self.lts_res_ver(client_id)

        if res_ver < src_ver:
            client_weights_list = self.get_clients_data(src_ver)

            # MATLAB: [associate,~]=kmeans(weightvector,cl_num);
            associate = compute_task_similarity_matlab(client_weights_list, self.n_clusters)

            # For each client, find similar clients and aggregate
            for ff in range(len(client_weights_list)):
                # MATLAB: CID = find(associate==associate(ff));
                cid_list = []
                for i, assoc_val in enumerate(associate):
                    if assoc_val == associate[ff]:
                        cid_list.append(i + 1)  # Convert to 1-based indexing for MATLAB compatibility

                # MATLAB: net_agg = model_agg(netsum,CID');
                aggregated_weights = model_agg_matlab(client_weights_list, cid_list)

                # Update result for this client
                self.clients_data.update_res(ff + 1, src_ver, aggregated_weights)

            self.gen_x_hat()

    def gen_x_hat(self):
        self.x_hat = lhs(self.dim, self.n_samples)

    def get_clients_data(self, src_ver):
        return [self.clients_data.get_src(cid, src_ver).data for cid in self.sorted_ids]
