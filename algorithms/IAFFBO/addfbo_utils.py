from collections import defaultdict

import numpy as np
from enum import Enum, auto
from pyDOE import lhs
from sklearn.cluster import KMeans
from sklearn.svm import SVR
from smt.surrogate_models import KRG, KPLS, GPX
from typing import Union, Any

T_Bound = Union[int, float, list, tuple, np.ndarray]


__all__ = [
    "Actions",
    "ClientsData",
    "Scaler",
    "SmtModel",
    "EnsembleRBF",
    "EnsembleRbfNetwork",
    "DifferentialEvolution"
]


def init_samples(x_lb, x_ub, dim: int, size: int):
    return lhs(dim, size) * (x_ub - x_lb) + x_lb


class Actions(Enum):
    PULL_INIT = auto()
    PUSH_UPDATE = auto()
    PULL_UPDATE = auto()


class VersionedData:
    def __init__(self):
        self.version = -1
        self.data = None


class ClientsData:
    def __init__(self):
        self.source = defaultdict(dict)
        self.result = defaultdict(VersionedData)

    def update_src(self, cid: int, version: int, data: Any):
        self.source[cid].update({version: data})

    def update_res(self, cid: int, version: int, data: Any):
        self.result[cid].version = version
        self.result[cid].data = data

    def get_src(self, cid: int, version: int) -> VersionedData:
        v_data = VersionedData()
        if version == -1:
            keys = self.source[cid].keys()
            version = max(keys) if keys else -1
        v_data.version = version
        v_data.data = self.source[cid].get(version)
        return v_data

    def get_res(self, cid: int) -> VersionedData:
        return self.result[cid]

    @property
    def available_src_ver(self) -> tuple[int, int]:
        vers = []
        for src in self.source.values():
            if len(src) > 0:
                vers.append(max(src.keys()))
            else:
                vers.append(-1)
        if not vers:
            vers.append(-1)
        return min(vers), len(vers)


class Scaler:
    def __init__(self):
        self._max = 1
        self._min = 0

    def normalize(self, a: np.ndarray):
        self._max = a.max()
        self._min = a.min()
        return (a - self._min) / (self._max - self._min)

    def denormalize(self, a: np.ndarray):
        return a * (self._max - self._min) + self._min


class EnsembleRbfNetwork:
    def __init__(self, gamma_scale=(1, 5), ensemble_size=10):
        self.data_size = 0
        self.is_fitted = False
        self.models = []
        self.gamma_scales = np.linspace(*gamma_scale, ensemble_size)
        self.x = None

    def fit(self, x: np.ndarray, y: np.ndarray) -> None:
        self.x = x.copy()
        _y = y.copy()
        C = 0.5 * np.median(_y)
        gamma = 1 / (x.shape[1] * x.var())
        self.models = [SVR(C=C, kernel='rbf', gamma=gamma*scale) for scale in self.gamma_scales]
        for model in self.models:
            model.fit(x, _y)
        self.is_fitted = True
        self.data_size = x.shape[0]

    def predict(self, x, return_std=False, indices: tuple[int]=None) -> tuple:
        """
        Predict by the average of models.
        """
        if indices is not None:
            y_pred = [self.models[i].predict(x).reshape(-1, 1) for i in indices]
        else:
            y_pred = [model.predict(x).reshape(-1, 1) for model in self.models]
        y_pred = np.hstack(y_pred)
        y_mean = np.mean(y_pred, axis=1)
        if return_std:
            y_std = np.std(y_pred, axis=1)
            return y_mean, y_std
        else:
            return y_mean


class EnsembleRBF:
    def __init__(self, model_params):
        self.models = [SVR() for _ in model_params]
        [model.__dict__.update(para) for model, para in zip(self.models, model_params)]

    def predict(self, x):
        mean = [model.predict(x).flatten() for model in self.models]
        return np.mean(mean, axis=0)


class SmtModel:
    def __init__(self, model_name='GPX', dim=None):

        if model_name == 'KRG':
            self.model = KRG(theta0=[1e-2], print_global=False)
        elif model_name == 'KPLS':
            self.model = KPLS(eval_n_comp=True, print_global=False)
        elif model_name == 'GPX':
            if dim is None:
                self.model = GPX(theta0=[1e-2], print_global=False)
            else:
                self.model = GPX(theta0=[1e-2], print_global=False, kpls_dim=dim)
        else:
            raise ValueError('Model name must be KRG or KPLS or GPX')
        self.is_fitted = False
        self.data_size = 0

    def fit(self, x: np.ndarray, y: np.ndarray):
        self.model.set_training_values(x, y)
        self.model.train()
        self.data_size = x.shape[0]
        self.is_fitted = True

    def predict(self, x, return_std=False):
        mean = self.model.predict_values(x)
        if return_std:
            mse = self.model.predict_variances(x)
            std = np.sqrt(mse)
            return mean, std
        return mean

    def to_dict(self):
        params = self.__dict__.copy()
        params['model'] = self.model.__dict__.copy()
        params['model'].pop('_gpx')
        return params

    def reconstruct(self, params):
        model_param = params.pop('model')
        self.__dict__.update(params)
        self.model.__dict__.update(model_param)
        self.model.train()


class RandomCluster:
    def __init__(self, dim, lb, ub, n_iter=20, n_cluster=10):
        self.dim = dim
        self.lb = lb
        self.ub = ub
        self.n_iter = n_iter
        self.n_cluster = n_cluster
        self.cluster_size = 5 * dim

    @staticmethod
    def sampling(centers: np.ndarray, n_samples: int, scale_factor: float = 0.1) -> np.ndarray:
            """
            以每个center为中心，在高维空间中采样n_samples个点

            参数：
                centers: shape (n_centers, dim) 的中心点矩阵
                n_samples: 每个中心点要采样的数量
                scale_factor: 控制采样范围的缩放因子(0-1)

            返回：
                samples: shape (n_centers * n_samples, dim) 的采样点矩阵
            """
            n_centers, dim = centers.shape

            # 自动确定协方差矩阵(使用欧氏距离的启发式方法)
            # 1. 计算所有中心点之间的平均距离
            pairwise_dist = np.sqrt(np.sum((centers[:, np.newaxis] - centers) ** 2, axis=-1))
            avg_dist = np.mean(pairwise_dist[pairwise_dist > 0])  # 排除对角线上的0

            # 2. 设置标准差(使用平均距离的scale_factor比例)
            std_dev = avg_dist * scale_factor

            # 3. 创建协方差矩阵(对角矩阵)
            cov = np.eye(dim) * (std_dev ** 2)

            # 为每个center采样
            samples = []
            for center in centers:
                samples.append(np.random.multivariate_normal(
                    mean=center,
                    cov=cov,
                    size=n_samples
                ))

            return np.vstack(samples)

    def clustering(self, x):
        # best_k = min(len(x), self.n_cluster)
        sse = []
        k_range = range(1, len(x) // 5)
        for k in k_range:
            kmeans = KMeans(n_clusters=k)
            kmeans.fit(x)
            sse.append(kmeans.inertia_)
        sse_diff = np.diff(sse)
        sse_diff_ratio = sse_diff[:-1] / sse_diff[1:]
        best_k = np.argmax(sse_diff_ratio) + 2
        kmeans = KMeans(n_clusters=best_k)
        kmeans.fit(x)
        centers = kmeans.cluster_centers_
        return centers

    def minimize(self, obj_func):
        all_x = lhs(self.dim, self.n_cluster * self.cluster_size) * (self.ub - self.lb) + self.lb
        all_y = obj_func(all_x).reshape(-1, 1)
        for _ in range(self.n_iter-1):
            y_vec = all_y.squeeze()
            y_mid = np.median(y_vec)
            indices = y_vec < y_mid
            filter_x = all_x[indices]
            centers = self.clustering(filter_x)
            new_x = self.sampling(centers, self.cluster_size)
            new_y = obj_func(new_x).reshape(-1, 1)
            all_x = np.vstack([all_x, new_x])
            all_y = np.vstack([all_y, new_y])
        idx = np.argmin(all_y.squeeze())
        return all_x[idx], all_y.squeeze()[idx]


class DifferentialEvolution:
    """
    Reference:
    Li, Jian-Yu, et al. “A Meta-Knowledge Transfer-Based
    Differential Evolution for Multitask Optimization.”
    (B) IEEE TEVC, Aug. 2022, pp. 719–34,
    https://doi.org/10.1109/tevc.2021.3131236.
    """
    def __init__(self,x_lb: T_Bound, x_ub: T_Bound, dim:int, max_gen=20, F=0.4, CR=0.9, num_elite=5, init_pop_size=200):
        self.x_lb = x_lb
        self.x_ub = x_ub
        self.dim = dim
        if isinstance(x_lb, (int, float)):
            self.x_bound = (self.x_lb, self.x_ub)
        elif isinstance(x_lb, (list, tuple, np.ndarray)):
            self.x_bound = (self.x_lb[0], self.x_ub[0])
        else:
            raise ValueError(f"Type of x_lb is {type(x_lb)}, but it should be int or float or iterable")
        self.max_gen = max_gen
        self.F = F
        self.CR = CR
        self.num_elite = num_elite
        self.init_pop_size = init_pop_size
        self.obj_func = None

    def minimize(self, obj_func, archive=None):
        assert callable(obj_func)
        self.obj_func = obj_func

        parents = init_samples(self.x_lb, self.x_ub, dim=self.dim, size=self.init_pop_size)
        if archive is not None:
            parents = np.vstack((parents, archive))

        parents, predictions = self._selection(parents)
        for _ in range(self.max_gen):
            v = self._mutation(parents, self.num_elite, self.F)
            u = self._crossover(v, parents, self.CR)
            candidates = np.vstack((v, u))
            parents, predictions = self._selection(candidates)

        return parents[0], predictions[0]

    def _mutation(self, parents, num_elite, F):
        """
            该实现是对参考文献所提的两种变异方式的折中（既有精英解的指导，又有随机解的多样）
            当 elite_rate=1 时，该算法退化为 DE/rand/1
            当 elite_rate=0 时，该算法退化为 DE/best/1
        """
        row, col = parents.shape

        c1 = np.random.randint(0, num_elite, row)
        c2 = np.random.randint(0, row, row)
        c3 = np.random.randint(0, row, row)

        r1 = parents[c1]
        r2 = parents[c2]
        r3 = parents[c3]

        res = r1 + F * (r2 - r3)
        res = np.clip(res, self.x_lb, self.x_ub)
        return res

    def _crossover(self, V, X, CR):
        shape = V.shape
        rand_mat = np.random.uniform(size=shape)
        res = np.zeros(shape)

        ind_leq_cr = rand_mat <= CR
        ind_greater_cr = rand_mat > CR
        ind_j_rand = self._create_j_rand_mat(shape)

        res[ind_leq_cr] = V[ind_leq_cr]
        res[ind_greater_cr] = X[ind_greater_cr]
        res[ind_j_rand] = V[ind_j_rand]

        return res

    def _selection(self, candidates):
        y = self.obj_func(candidates)
        y = y.flatten()
        sorted_index = np.argsort(y)

        sorted_candidates = candidates[sorted_index]
        sorted_y = y[sorted_index]

        return sorted_candidates[:self.init_pop_size], sorted_y[:self.init_pop_size]

    @staticmethod
    def _create_j_rand_mat(shape):
        """
        Creates a boolean mask matrix of given shape where
        each row has exactly one True at random positions.

        Used in crossover operation when j=j_rand to ensure
        each offspring inherits at least one dimension value
        from the mutated individual.

        Args:
            shape (tuple): The desired shape of the output
             matrix (rows, columns)

        Returns:
            np.ndarray: A boolean mask matrix with shape `shape`
        """
        j_rand_mask = np.zeros(shape=shape, dtype=bool)
        col_indices = np.random.randint(0, shape[1], shape[0])
        row_indices = np.arange(shape[0])
        j_rand_mask[row_indices, col_indices] = True
        return j_rand_mask