"""
FMDLES Algorithm Utilities

This module contains the core components of the FMDLES algorithm:
- DLESNetwork: Deep learning enhanced search path reconstruction neural network
- UMDAc: Univariate Marginal Distribution Algorithm for continuous optimization
- DLES: Deep Learning Enhanced Search algorithm
- Utility functions for federated learning
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from scipy.stats import norm, pearsonr
from typing import List, Dict, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')


class DLESNetwork(nn.Module):
    """
    Deep Learning Enhanced Search Path Reconstruction Neural Network
    Network structure: 1-2D-4D-D (Net3 structure)
    """
    def __init__(self, dimension: int):
        super(DLESNetwork, self).__init__()
        self.dimension = dimension
        
        # Network layers
        self.input_layer = nn.Linear(1, 2 * dimension)
        self.hidden_layer1 = nn.Linear(2 * dimension, 4 * dimension)
        self.output_layer = nn.Linear(4 * dimension, dimension)
        
        # Activation function
        self.tanh = nn.Tanh()
        
    def forward(self, fitness):
        """
        Forward propagation
        Args:
            fitness: Fitness values (batch_size, 1)
        Returns:
            predicted_individual: Predicted individual (batch_size, dimension)
        """
        x = self.input_layer(fitness)
        x = self.tanh(self.hidden_layer1(x))
        x = self.output_layer(x)
        return x


class UMDAc:
    """
    Univariate Marginal Distribution Algorithm for continuous optimization (UMDAc)
    """
    def __init__(self, dimension: int, population_size: int):
        self.dimension = dimension
        self.population_size = population_size
        
    def generate_offspring(self, population: np.ndarray, fitness_values: np.ndarray) -> np.ndarray:
        """
        Generate offspring individuals
        Args:
            population: Current population (N, D)
            fitness_values: Fitness values (N,)
        Returns:
            offspring: Offspring population (N, D)
        """
        N = len(population)
        
        # Calculate weights
        weights_prime = np.array([np.log(N + 0.5) - np.log(i + 1) for i in range(N)])
        weights = weights_prime / np.sum(weights_prime)
        
        # Calculate weighted mean and standard deviation
        mu = np.zeros(self.dimension)
        for d in range(self.dimension):
            mu[d] = np.sum(weights * population[:, d])
        
        # Calculate standard deviation
        sigma = np.zeros(self.dimension)
        for d in range(self.dimension):
            sigma[d] = np.sqrt(np.sum(weights * (population[:, d] - mu[d])**2))
            sigma[d] = max(sigma[d], 1e-8)  # Avoid zero standard deviation
        
        # Generate offspring
        offspring = np.random.normal(mu, sigma, (N, self.dimension))
        
        return offspring


def fitness_mse_loss(predictions: torch.Tensor, targets: torch.Tensor, 
                    fitness_values: torch.Tensor) -> torch.Tensor:
    """
    Fitness Mean Squared Error loss function (FMSE)
    Args:
        predictions: Network predictions (batch_size, dimension)
        targets: True values (batch_size, dimension)
        fitness_values: Fitness values (batch_size,)
    Returns:
        loss: FMSE loss value
    """
    mse = torch.mean((predictions - targets)**2, dim=1)  # MSE for each sample
    fmse = torch.mean(mse + fitness_values)  # Add fitness weight
    return fmse


def compute_trajectory_similarity(trajectory_data_1: Dict, trajectory_data_2: Dict) -> float:
    """
    Compute trajectory similarity between two clients using Pearson correlation
    Args:
        trajectory_data_1: Trajectory data from client 1
        trajectory_data_2: Trajectory data from client 2
    Returns:
        similarity: Similarity score between 0 and 1
    """
    rates_1 = trajectory_data_1.get('improvement_rates', [])
    rates_2 = trajectory_data_2.get('improvement_rates', [])
    
    if len(rates_1) > 1 and len(rates_2) > 1:
        # Align sequence lengths
        min_len = min(len(rates_1), len(rates_2))
        rates_1_aligned = rates_1[:min_len]
        rates_2_aligned = rates_2[:min_len]
        
        # Calculate Pearson correlation coefficient
        try:
            correlation, p_value = pearsonr(rates_1_aligned, rates_2_aligned)
            # Handle NaN values, set to 0 similarity
            if np.isnan(correlation):
                correlation = 0.0
            # Convert correlation to similarity (take absolute value)
            similarity = abs(correlation)
        except:
            similarity = 0.0
    else:
        similarity = 0.0
    
    return similarity


def aggregate_models_weighted(models: List[Dict], weights: List[float]) -> Dict:
    """
    Aggregate multiple models using weighted averaging
    Args:
        models: List of model state dictionaries
        weights: List of weights for each model
    Returns:
        aggregated_model: Aggregated model state dictionary
    """
    if not models or not weights:
        return {}
    
    # Normalize weights
    total_weight = sum(weights)
    if total_weight == 0:
        return models[0]
    
    normalized_weights = [w / total_weight for w in weights]
    
    # Initialize aggregated model
    aggregated_model = {}
    for key in models[0].keys():
        aggregated_model[key] = torch.zeros_like(models[0][key])
    
    # Weighted aggregation
    for i, (model, weight) in enumerate(zip(models, normalized_weights)):
        for key in aggregated_model.keys():
            aggregated_model[key] += weight * model[key]
    
    return aggregated_model


class FMDLESActions:
    """Action types for FMDLES client-server communication"""
    # Phase 1: Exploration phase
    PUSH_EXPLORATION_RESULTS = "push_exploration_results"
    PULL_EXPLORATION_STATS = "pull_exploration_stats"

    # Phase 2: Model training and aggregation
    PUSH_LOCAL_MODEL = "push_local_model"
    PULL_AGGREGATED_MODEL = "pull_aggregated_model"

    # Phase 3: Final exploitation
    PUSH_FINAL_RESULTS = "push_final_results"
    PULL_FINAL_STATS = "pull_final_stats"

    # Utility actions
    PULL_SIMILARITY_MATRIX = "pull_similarity_matrix"
    REQUEST_PERSONALIZED_MODEL = "request_personalized_model"


def normalize_objectives(y: np.ndarray) -> np.ndarray:
    """Normalize objective values to [0, 1]"""
    y_min, y_max = y.min(), y.max()
    if y_max - y_min < 1e-10:
        return np.zeros_like(y)
    return (y - y_min) / (y_max - y_min)


def get_unique_indices(x: np.ndarray) -> np.ndarray:
    """Get indices of unique rows in x"""
    _, unique_indices = np.unique(x, axis=0, return_index=True)
    return np.sort(unique_indices)


class DLES:
    """
    Deep Learning Enhanced Search algorithm (DLES)
    Core algorithm for single-task optimization
    """
    def __init__(self, objective_function, dimension: int, population_size: int = 50,
                 max_evaluations: int = 10000, exploration_iterations: int = 100,
                 learning_rate: float = 0.5, epochs: int = 10):
        """
        Initialize DLES algorithm
        Args:
            objective_function: Objective function
            dimension: Problem dimension
            population_size: Population size
            max_evaluations: Maximum evaluations
            exploration_iterations: Exploration iterations
            learning_rate: Learning rate
            epochs: Network training epochs
        """
        self.objective_function = objective_function
        self.dimension = dimension
        self.population_size = population_size
        self.max_evaluations = max_evaluations
        self.exploration_iterations = exploration_iterations
        self.learning_rate = learning_rate
        self.epochs = epochs

        # Algorithm state
        self.evaluations = 0
        self.best_fitness = float('inf')
        self.best_individual = None
        self.dataset = []  # Store (individual, fitness) pairs

        # Initialize components
        self.umda = UMDAc(dimension, population_size)
        self.network = DLESNetwork(dimension)
        self.optimizer = optim.Adam(self.network.parameters(), lr=learning_rate)

    def evaluate(self, individuals):
        """
        Evaluate individual fitness
        Args:
            individuals: Individual array (N, D) or (D,)
        Returns:
            fitness_values: Fitness values
        """
        if individuals.ndim == 1:
            individuals = individuals.reshape(1, -1)

        fitness_values = []
        for individual in individuals:
            if self.evaluations >= self.max_evaluations:
                break
            else:
                fitness = self.objective_function(individual)
                fitness_values.append(fitness)
                self.evaluations += 1

        return np.array(fitness_values)

    def exploration_sampling(self):
        """
        Exploration sampling phase - Algorithm 1
        Use UMDAc for search space exploration and data collection
        """
        # Initialize population
        if hasattr(self.objective_function, 'x_bound'):
            bounds = self.objective_function.x_bound
            if isinstance(bounds, list) and len(bounds) == 2:
                # Uniform bounds
                population = np.random.uniform(bounds[0], bounds[1],
                                             (self.population_size, self.dimension))
            else:
                # Different bounds per dimension
                population = np.random.uniform(bounds[:, 0], bounds[:, 1],
                                             (self.population_size, self.dimension))
        else:
            # Default bounds [-100, 100]
            population = np.random.uniform(-100, 100,
                                         (self.population_size, self.dimension))

        # Evaluate initial population
        fitness_values = self.evaluate(population)

        # Find best individual
        best_idx = np.argmin(fitness_values)
        self.best_fitness = fitness_values[best_idx]
        self.best_individual = population[best_idx].copy()

        # Add to dataset
        self.dataset.append((self.best_individual.copy(), self.best_fitness))

        # Iterative exploration
        for t in range(self.exploration_iterations):
            if self.evaluations >= self.max_evaluations:
                break

            # Sort population
            sorted_indices = np.argsort(fitness_values)
            population = population[sorted_indices]
            fitness_values = fitness_values[sorted_indices]

            # Generate offspring
            offspring = self.umda.generate_offspring(population, fitness_values)

            # Evaluate offspring
            offspring_fitness = self.evaluate(offspring)

            if len(offspring_fitness) == 0:
                break

            # Use only actually evaluated offspring
            actual_offspring = offspring[:len(offspring_fitness)]

            # Combine populations
            combined_population = np.vstack([population, actual_offspring])
            combined_fitness = np.hstack([fitness_values, offspring_fitness])

            # Select next generation
            sorted_indices = np.argsort(combined_fitness)
            population = combined_population[sorted_indices[:self.population_size]]
            fitness_values = combined_fitness[sorted_indices[:self.population_size]]

            # Check for better solution
            current_best_fitness = fitness_values[0]
            if current_best_fitness < self.best_fitness:
                self.best_fitness = current_best_fitness
                self.best_individual = population[0].copy()
                # Only add to dataset when finding better solution
                self.dataset.append((self.best_individual.copy(), self.best_fitness))

    def network_reconstruction(self):
        """
        Network reconstruction phase - Algorithm 2
        Train neural network to reconstruct search path
        """
        if len(self.dataset) < 2:
            return

        # Prepare training data
        individuals = np.array([item[0] for item in self.dataset])
        fitness_values = np.array([item[1] for item in self.dataset])

        # Convert to PyTorch tensors
        X_fitness = torch.FloatTensor(fitness_values).reshape(-1, 1)
        y_individuals = torch.FloatTensor(individuals)

        # Train network
        self.network.train()
        for epoch in range(self.epochs):
            self.optimizer.zero_grad()

            # Forward propagation
            predictions = self.network(X_fitness)

            # Calculate FMSE loss
            loss = fitness_mse_loss(predictions, y_individuals,
                                  torch.FloatTensor(fitness_values))

            # Backward propagation
            loss.backward()
            self.optimizer.step()

    def predict_individual(self, fitness_value: float) -> np.ndarray:
        """
        Use trained network to predict individual
        Args:
            fitness_value: Fitness value
        Returns:
            predicted_individual: Predicted individual
        """
        self.network.eval()
        try:
            with torch.no_grad():
                fitness_tensor = torch.FloatTensor([fitness_value]).reshape(-1, 1)
                predicted = self.network(fitness_tensor)
                return predicted.numpy().flatten()
        except RuntimeError as e:
            if "unknown parameter type" in str(e):
                # Fallback for PyTorch compatibility issues
                fitness_tensor = torch.FloatTensor([fitness_value]).reshape(-1, 1)
                predicted = self.network(fitness_tensor)
                return predicted.detach().numpy().flatten()
            else:
                raise e
