# FMDLES Algorithm

## Overview

FMDLES (Federated Multi-task Deep Learning Enhanced Search) is a federated optimization algorithm that combines deep learning enhanced search path reconstruction with federated learning for privacy-preserving multi-task optimization.

## Key Features

- **Three-phase optimization**: Exploration, Training, and Exploitation
- **Deep learning enhanced search**: Uses neural networks to reconstruct search paths
- **Federated learning**: Supports distributed optimization with privacy protection
- **Personalized aggregation**: Clients receive personalized models based on task similarity
- **IID/Non-IID support**: Handles both independent and non-independent data distributions

## Algorithm Components

### Core Components

1. **DLESNetwork**: Deep learning enhanced search path reconstruction neural network
   - Network structure: 1-2D-4D-D (Net3 structure)
   - Maps fitness values to predicted individuals

2. **UMDAc**: Univariate Marginal Distribution Algorithm for continuous optimization
   - Generates offspring based on weighted population statistics

3. **DLES**: Main single-task optimization algorithm
   - Phase 1: Exploration sampling using UMDAc
   - Phase 2: Network reconstruction training
   - Phase 3: Hybrid exploitation (CMA-ES + univariate sampling)

### Federated Components

1. **FMDLESClient**: Federated client implementation
   - Performs local exploration using DLES
   - Trains local models on collected data
   - Participates in federated aggregation
   - Uses global model for final exploitation

2. **FMDLESServer**: Federated server implementation
   - Aggregates exploration results from clients
   - Computes client similarity based on optimization trajectories
   - Performs personalized federated averaging
   - Provides personalized global models to clients

## Usage

### Basic Usage

```python
from pyfmto.problems.benchmarks import Sphere
from algorithms.FMDLES import FMDLESClient, FMDLESServer

# Create a test problem
problem = Sphere(dim=10, fe_max=110)

# Create client
client = FMDLESClient(
    problem=problem,
    max_fe=110,
    exploration_ratio=0.7,
    population_size=10,
    exploration_iterations=20,
    learning_rate=0.3,
    epochs=3,
    iid=1,  # Use IID data distribution
    np_per_dim=2
)

# Create server
server = FMDLESServer(
    similarity_threshold=0.1,
    top_k_similar=3,
    use_personalized_aggregation=1
)

# Run optimization
server.start()  # Start server in separate process
result = client.start()  # Run client optimization
```

### Parameters

#### Client Parameters

- `max_fe`: Maximum function evaluations (default: 110)
- `exploration_ratio`: Ratio of evaluations for exploration phase (default: 0.7)
- `population_size`: Population size for DLES (default: 10)
- `exploration_iterations`: Number of exploration iterations (default: 20)
- `learning_rate`: Learning rate for neural network (default: 0.3)
- `epochs`: Training epochs for local model (default: 3)
- `iid`: Whether to use IID data distribution (1 for True, 0 for False)
- `np_per_dim`: Number of partitions per dimension for Non-IID (default: 2)

#### Server Parameters

- `similarity_threshold`: Minimum similarity for client aggregation (default: 0.1)
- `top_k_similar`: Maximum number of similar clients to aggregate (default: 3)
- `use_personalized_aggregation`: Whether to use personalized aggregation (1 for True, 0 for False)

## Algorithm Phases

### Phase 1: Exploration
- Each client performs local exploration using DLES algorithm
- Collects optimization trajectory data for similarity computation
- Shares aggregated statistics (not raw data) with server

### Phase 2: Training and Aggregation
- Clients train local neural network models on collected data
- Server computes client similarity based on optimization trajectories
- Server performs personalized federated aggregation
- Clients receive personalized global models

### Phase 3: Exploitation
- Clients use global models to predict optimal solutions
- Perform local refinement using CMA-ES and univariate sampling
- Achieve final optimization results

## Testing

Run the test script to verify the implementation:

```bash
python test_fmdles.py
```

The test includes:
1. Component tests for individual algorithm parts
2. Basic federated optimization test with multiple clients

## Implementation Notes

- The algorithm is designed for continuous optimization problems
- Supports both single-objective optimization
- Uses PyTorch for neural network implementation
- Compatible with the pyfmto framework architecture
- Handles client-server communication through HTTP requests

## References

Based on the paper: "Learning-assisted Search Path Reconstruction Empowers Evolution Algorithm for Optimization" and federated multi-task optimization principles.
