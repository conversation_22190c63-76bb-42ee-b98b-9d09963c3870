"""
FMDLES Server Implementation

This module implements the FMDLES server that integrates with the pyfmto framework.
The server performs federated aggregation with personalized model generation.
"""

import numpy as np
import torch
import copy
from typing import Dict, List, Optional
from collections import defaultdict
import logging
from scipy.stats import pearsonr

from pyfmto.framework import Server, ClientPackage, ServerPackage
from .fmdles_utils import (
    DLESNetwork,
    FMDLESActions,
    compute_trajectory_similarity,
    aggregate_models_weighted
)

logger = logging.getLogger(__name__)


class FMDLESServer(Server):
    """
    similarity_threshold: 0.1
    top_k_similar: 3
    use_personalized_aggregation: 1
    """

    def __init__(self, **kwargs):
        super().__init__()
        kwargs = self.update_kwargs(kwargs)

        # Server parameters
        self.similarity_threshold = kwargs.get('similarity_threshold', 0.1)
        self.top_k_similar = kwargs.get('top_k_similar', 3)
        self.use_personalized_aggregation = bool(kwargs.get('use_personalized_aggregation', 1))

        # Data storage
        self.exploration_results = {}  # client_id -> exploration data
        self.client_models = {}        # client_id -> model data
        self.trajectory_data = {}      # client_id -> trajectory data
        self.similarity_matrix = None
        self.personalized_models = {}  # client_id -> personalized model
        self.final_results = {}        # client_id -> final results

        # Statistics
        self.exploration_stats = {}
        self.final_stats = {}

        # Set aggregation interval
        self.set_agg_interval(0.1)

    def handle_request(self, client_data: ClientPackage) -> ServerPackage:
        """Handle client requests"""
        action_handlers = {
            FMDLESActions.PUSH_EXPLORATION_RESULTS: self._handle_push_exploration_results,
            FMDLESActions.PULL_EXPLORATION_STATS: self._handle_pull_exploration_stats,
            FMDLESActions.PUSH_LOCAL_MODEL: self._handle_push_local_model,
            FMDLESActions.PULL_AGGREGATED_MODEL: self._handle_pull_aggregated_model,
            FMDLESActions.PUSH_FINAL_RESULTS: self._handle_push_final_results,
            FMDLESActions.PULL_FINAL_STATS: self._handle_pull_final_stats,
        }

        handler = action_handlers.get(client_data.action)
        if handler:
            return handler(client_data)
        else:
            logger.warning(f"Unknown action: {client_data.action}")
            return ServerPackage('error', {'status': 'unknown_action'})

    def _handle_push_exploration_results(self, client_data: ClientPackage) -> ServerPackage:
        """Handle client pushing exploration results"""
        client_id = client_data.cid
        data = client_data.data

        if data:
            self.exploration_results[client_id] = data
            self.trajectory_data[client_id] = data.get('trajectory_data', {})
            logger.info(f"Received exploration results from client {client_id}")
            return ServerPackage('success', {'status': 'exploration_results_received'})
        else:
            return ServerPackage('error', {'status': 'invalid_data'})

    def _handle_pull_exploration_stats(self, client_data: ClientPackage) -> ServerPackage:
        """Handle client pulling exploration statistics"""
        if len(self.exploration_results) >= self.num_clients:
            # All clients have reported, compute statistics
            if not self.exploration_stats:
                self._compute_exploration_stats()
            return ServerPackage('success', self.exploration_stats)
        else:
            return ServerPackage('waiting', {'status': 'waiting_for_all_clients'})

    def _handle_push_local_model(self, client_data: ClientPackage) -> ServerPackage:
        """Handle client pushing local model"""
        client_id = client_data.cid
        data = client_data.data

        if data and 'model_state_dict' in data:
            self.client_models[client_id] = data
            logger.info(f"Received local model from client {client_id}")
            return ServerPackage('success', {'status': 'model_received'})
        else:
            return ServerPackage('error', {'status': 'invalid_model_data'})

    def _handle_pull_aggregated_model(self, client_data: ClientPackage) -> ServerPackage:
        """Handle client pulling aggregated model"""
        client_id = client_data.cid

        if len(self.client_models) >= self.num_clients:
            # All models received, perform aggregation
            if not self.personalized_models:
                self._aggregate_models()

            if self.use_personalized_aggregation and client_id in self.personalized_models:
                model_data = {'aggregated_model': self.personalized_models[client_id]}
            elif hasattr(self, 'global_model'):
                model_data = {'aggregated_model': self.global_model}
            else:
                model_data = {'aggregated_model': None}

            return ServerPackage('success', model_data)
        else:
            return ServerPackage('waiting', {'aggregated_model': None})

    def _handle_push_final_results(self, client_data: ClientPackage) -> ServerPackage:
        """Handle client pushing final results"""
        client_id = client_data.cid
        data = client_data.data

        if data:
            self.final_results[client_id] = data
            logger.info(f"Received final results from client {client_id}")
            return ServerPackage('success', {'status': 'final_results_received'})
        else:
            return ServerPackage('error', {'status': 'invalid_final_data'})

    def _handle_pull_final_stats(self, client_data: ClientPackage) -> ServerPackage:
        """Handle client pulling final statistics"""
        if len(self.final_results) >= self.num_clients:
            if not self.final_stats:
                self._compute_final_stats()
            return ServerPackage('success', self.final_stats)
        else:
            return ServerPackage('waiting', {'status': 'waiting_for_final_results'})

    def _compute_exploration_stats(self):
        """Compute global exploration statistics"""
        if not self.exploration_results:
            return

        all_best_fitness = [result['best_fitness'] for result in self.exploration_results.values()]
        total_evaluations = sum(result['evaluations_used'] for result in self.exploration_results.values())
        total_dataset_size = sum(result['dataset_size'] for result in self.exploration_results.values())

        self.exploration_stats = {
            'total_clients': len(self.exploration_results),
            'global_best_fitness': min(all_best_fitness),
            'global_worst_fitness': max(all_best_fitness),
            'average_fitness': np.mean(all_best_fitness),
            'fitness_std': np.std(all_best_fitness),
            'total_evaluations': total_evaluations,
            'total_dataset_size': total_dataset_size,
            'average_dataset_size': total_dataset_size / len(self.exploration_results)
        }

        logger.info(f"Exploration stats computed: best={self.exploration_stats['global_best_fitness']:.6e}")

    def _compute_trajectory_similarity_matrix(self) -> np.ndarray:
        """Compute trajectory similarity matrix between clients"""
        client_ids = list(self.trajectory_data.keys())
        n_clients = len(client_ids)
        similarity_matrix = np.eye(n_clients)

        for i in range(n_clients):
            for j in range(i + 1, n_clients):
                client_i = client_ids[i]
                client_j = client_ids[j]

                # Get improvement rate sequences
                rates_i = self.trajectory_data[client_i].get('improvement_rates', [])
                rates_j = self.trajectory_data[client_j].get('improvement_rates', [])

                if len(rates_i) > 1 and len(rates_j) > 1:
                    # Align sequence lengths
                    min_len = min(len(rates_i), len(rates_j))
                    rates_i_aligned = rates_i[:min_len]
                    rates_j_aligned = rates_j[:min_len]

                    # Calculate Pearson correlation coefficient
                    try:
                        correlation, p_value = pearsonr(rates_i_aligned, rates_j_aligned)
                        if np.isnan(correlation):
                            correlation = 0.0
                        similarity = abs(correlation)
                    except:
                        similarity = 0.0
                else:
                    similarity = 0.0

                similarity_matrix[i, j] = similarity
                similarity_matrix[j, i] = similarity

        self.similarity_matrix = similarity_matrix
        logger.info(f"Similarity matrix computed, shape: {similarity_matrix.shape}")
        return similarity_matrix

    def _aggregate_models(self):
        """Aggregate client models"""
        if not self.client_models:
            return

        logger.info("Starting model aggregation...")

        if self.use_personalized_aggregation:
            self._personalized_aggregation()
        else:
            self._standard_aggregation()

        logger.info("Model aggregation completed")

    def _personalized_aggregation(self):
        """Perform personalized federated aggregation"""
        # Compute similarity matrix
        if self.similarity_matrix is None:
            self._compute_trajectory_similarity_matrix()

        client_ids = list(self.client_models.keys())
        client_id_to_idx = {cid: idx for idx, cid in enumerate(client_ids)}

        # For each client, create personalized model
        for target_client_id in client_ids:
            target_idx = client_id_to_idx[target_client_id]

            # Get similarities with other clients
            similarities = self.similarity_matrix[target_idx, :]

            # Select similar clients
            similar_indices = [target_idx]  # Include self
            similar_weights = [1.0]  # Self weight

            # Add most similar clients
            other_similarities = []
            for other_idx, other_client_id in enumerate(client_ids):
                if other_idx != target_idx:
                    sim = similarities[other_idx]
                    if sim >= self.similarity_threshold:
                        other_similarities.append((other_idx, sim))

            other_similarities.sort(key=lambda x: x[1], reverse=True)

            # Take top-k similar clients
            for other_idx, sim in other_similarities[:self.top_k_similar]:
                similar_indices.append(other_idx)
                similar_weights.append(sim)

            # Aggregate models from similar clients
            models_to_aggregate = []
            weights_to_use = []

            for idx, weight in zip(similar_indices, similar_weights):
                client_id = client_ids[idx]
                model_data = self.client_models[client_id]
                dataset_weight = model_data['dataset_size']
                combined_weight = weight * dataset_weight

                models_to_aggregate.append(model_data['model_state_dict'])
                weights_to_use.append(combined_weight)

            # Perform weighted aggregation
            if models_to_aggregate:
                aggregated_model = aggregate_models_weighted(models_to_aggregate, weights_to_use)
                self.personalized_models[target_client_id] = aggregated_model

        logger.info(f"Personalized aggregation completed for {len(self.personalized_models)} clients")

    def _standard_aggregation(self):
        """Perform standard federated averaging"""
        if not self.client_models:
            return

        # Calculate weights based on dataset sizes
        total_samples = sum(model['dataset_size'] for model in self.client_models.values())
        weights = [model['dataset_size'] / total_samples for model in self.client_models.values()]

        # Get model state dictionaries
        models = [model['model_state_dict'] for model in self.client_models.values()]

        # Aggregate models
        self.global_model = aggregate_models_weighted(models, weights)
        logger.info("Standard aggregation completed")

    def _compute_final_stats(self):
        """Compute final optimization statistics"""
        if not self.final_results:
            return

        final_fitness = [result['final_best_fitness'] for result in self.final_results.values()]
        total_evaluations = sum(result['final_evaluations'] for result in self.final_results.values())
        improved_clients = sum(1 for result in self.final_results.values() 
                             if result.get('improvement_from_federation', False))

        self.final_stats = {
            'total_clients': len(self.final_results),
            'global_best_fitness': min(final_fitness),
            'global_worst_fitness': max(final_fitness),
            'average_final_fitness': np.mean(final_fitness),
            'final_fitness_std': np.std(final_fitness),
            'total_final_evaluations': total_evaluations,
            'clients_improved_by_federation': improved_clients,
            'federation_improvement_rate': improved_clients / len(self.final_results)
        }

        logger.info(f"Final stats computed: best={self.final_stats['global_best_fitness']:.6e}")

    def aggregate(self, client_id: int):
        """Aggregate data (called by framework)"""
        # This method is called periodically by the framework
        # Most aggregation is handled in the request handlers
        pass

    @property
    def sorted_ids(self):
        """Get sorted client IDs"""
        return sorted(self._active_clients)
