#!/usr/bin/env python3
"""
FMDLES Algorithm Test Script

This script tests the FMDLES algorithm implementation in the pyfmto framework.
It creates a simple federated optimization scenario with multiple clients.
"""

import sys
import os
import numpy as np
import multiprocessing as mp
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))
sys.path.insert(0, str(Path(__file__).parent.parent))

from pyfmto.problems import SingleTaskProblem
from FMDLES import FMDLESClient, FMDLESServer


class TestProblem(SingleTaskProblem):
    """Simple test problem for optimization"""
    def __init__(self, client_id, dimension=10, max_fe=110):
        # Create different shifts for different clients to simulate different tasks
        self.shift = np.random.RandomState(client_id).uniform(-2, 2, dimension)

        super().__init__(
            dim=dimension,
            obj=1,
            x_lb=np.full(dimension, -10.0),
            x_ub=np.full(dimension, 10.0),
            fe_init=10,
            fe_max=max_fe
        )
        self.set_id(client_id)

    def _eval_single(self, x):
        """Shifted sphere function"""
        shifted_x = x - self.shift
        return np.sum(shifted_x ** 2)


def create_test_problem(client_id, dimension=10, max_fe=110):
    """Create a test problem for a client"""
    return TestProblem(client_id, dimension, max_fe)


def run_client(client_id, dimension=10, max_fe=110):
    """Run a single client"""
    print(f"Starting client {client_id}")
    
    try:
        # Create problem
        problem = create_test_problem(client_id, dimension, max_fe)
        
        # Create client
        client = FMDLESClient(
            problem=problem,
            max_fe=max_fe,
            exploration_ratio=0.7,
            population_size=10,
            exploration_iterations=20,
            learning_rate=0.3,
            epochs=3,
            iid=1,  # Use 1 instead of True
            np_per_dim=2
        )
        
        # Run optimization
        result = client.start()
        print(f"Client {client_id} completed with best fitness: {client.solutions.y_min:.6e}")
        return result
        
    except Exception as e:
        print(f"Client {client_id} failed with error: {e}")
        import traceback
        traceback.print_exc()
        return None


def run_server():
    """Run the server"""
    print("Starting FMDLES server")
    
    try:
        server = FMDLESServer(
            similarity_threshold=0.1,
            top_k_similar=3,
            use_personalized_aggregation=1  # Use 1 instead of True
        )
        
        server.start()
        
    except Exception as e:
        print(f"Server failed with error: {e}")
        import traceback
        traceback.print_exc()


def test_fmdles_basic():
    """Basic test of FMDLES algorithm"""
    print("=" * 60)
    print("FMDLES Algorithm Basic Test")
    print("=" * 60)
    
    # Test parameters
    num_clients = 4
    dimension = 10
    max_fe = 110
    
    print(f"Test configuration:")
    print(f"  Number of clients: {num_clients}")
    print(f"  Problem dimension: {dimension}")
    print(f"  Max function evaluations per client: {max_fe}")
    print()
    
    # Start server in a separate process
    server_process = mp.Process(target=run_server)
    server_process.start()
    
    # Wait a moment for server to start
    import time
    time.sleep(2)
    
    # Start clients
    client_processes = []
    for client_id in range(1, num_clients + 1):
        client_process = mp.Process(
            target=run_client,
            args=(client_id, dimension, max_fe)
        )
        client_processes.append(client_process)
        client_process.start()
    
    # Wait for all clients to complete
    for client_process in client_processes:
        client_process.join()
    
    # Terminate server
    server_process.terminate()
    server_process.join()
    
    print("\nTest completed!")


def test_fmdles_components():
    """Test individual components of FMDLES"""
    print("=" * 60)
    print("FMDLES Components Test")
    print("=" * 60)
    
    from fmdles_utils import DLESNetwork, UMDAc, DLES
    
    # Test DLESNetwork
    print("Testing DLESNetwork...")
    dimension = 5
    network = DLESNetwork(dimension)
    
    # Test forward pass
    import torch
    fitness_input = torch.FloatTensor([[1.0], [2.0], [3.0]])
    output = network(fitness_input)
    print(f"  Network input shape: {fitness_input.shape}")
    print(f"  Network output shape: {output.shape}")
    assert output.shape == (3, dimension), f"Expected shape (3, {dimension}), got {output.shape}"
    print("  ✓ DLESNetwork test passed")
    
    # Test UMDAc
    print("Testing UMDAc...")
    umda = UMDAc(dimension, population_size=10)
    population = np.random.randn(10, dimension)
    fitness_values = np.random.rand(10)
    offspring = umda.generate_offspring(population, fitness_values)
    print(f"  Population shape: {population.shape}")
    print(f"  Offspring shape: {offspring.shape}")
    assert offspring.shape == population.shape, f"Expected shape {population.shape}, got {offspring.shape}"
    print("  ✓ UMDAc test passed")
    
    # Test DLES (basic initialization)
    print("Testing DLES initialization...")
    def test_func(x):
        return np.sum(x ** 2)

    dles = DLES(
        objective_function=test_func,
        dimension=dimension,
        population_size=10,
        max_evaluations=50,
        exploration_iterations=5,
        learning_rate=0.1,
        epochs=2
    )
    print(f"  DLES dimension: {dles.dimension}")
    print(f"  DLES population size: {dles.population_size}")
    print("  ✓ DLES initialization test passed")
    
    print("\nAll component tests passed!")


if __name__ == "__main__":
    # Test components first
    test_fmdles_components()
    
    print("\n" + "=" * 60)
    print()
    
    # Test basic functionality
    test_fmdles_basic()
