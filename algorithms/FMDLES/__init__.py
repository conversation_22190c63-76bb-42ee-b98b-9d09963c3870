"""
FMDLES (Federated Multi-task Deep Learning Enhanced Search) Algorithm

This module implements the FMDLES algorithm for federated multi-task optimization.
The algorithm combines deep learning enhanced search path reconstruction with 
federated learning for privacy-preserving multi-task optimization.

Key Components:
- FMDLESClient: Federated client implementing DLES algorithm
- FMDLESServer: Federated server with personalized aggregation
- DLES utilities: Core algorithm components

Authors: <AUTHORS>
"""

from .fmdles_client import FMDLESClient
from .fmdles_server import FMDLESServer

__all__ = ['FMDLESClient', 'FMDLESServer']
